@import './variables.scss';
@import './mixin.scss';

html,
body #app {
  color: #333333;
  font-family: PingFangSC, PingFangSC-Regular;
  height: 100%;
  margin: 0;
  padding: 0;
  // font-family: Arial, Helvetica, 'STHeiti STXihei', 'Microsoft YaHei', Tohoma, sans-serif;
  background-color: $background-color;
  // min-width: 320px;
  // max-width: $max-width;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}
.top-banner {
  height: 35px;
  background: #116aff;
  font-size: 16px;
  font-family: PingFangSC-Ultralight, sans-serif;
  font-weight: bold;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  .icon {
    position: absolute;
    left: 5px;
  }
  .submit {
    position: absolute;
    right: 10px;
  }
}
