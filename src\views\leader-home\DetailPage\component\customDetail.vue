<template>
  <div class="item-detail">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div style="display: flex; align-items: center">
        <div class="info-text">检查科室：</div>
        <div class="dept-text">{{ itemDetail.department ? itemDetail.department : '--' }}</div>
        <div class="info-text">指标分值：</div>
        <div class="score-text">{{ itemDetail.score ? itemDetail.score : '--' }}</div>
        <div v-if="itemDetail.selfScore" class="info-text">院内自评分数：</div>
        <div v-if="itemDetail.selfScore" class="score-text">
          {{ itemDetail.selfScore ? itemDetail.selfScore : '--' }}
        </div>
      </div>
    </div>
    <van-divider :style="{ color: '#EEEEEE' }"></van-divider>
    <div v-if="itemDetail.status === true" class="detail-desc">
      <div>
        <div>{{ itemDetail.name }}</div>
        <div v-if="itemDetail.core" class="core-text">★核心指标</div>
        <div class="detail-info">
          <!-- 当前节点的items数据 -->
          <div v-if="itemDetail.custom" style="display: flex; flex-direction: column">
            <!-- 多根节点的树 -->
            <div v-for="(item, index) in itemDetail.custom" :key="index">
              <tree-item
                :disabled="true"
                :item="item"
                @file-preview="filePreview"
                @custom-item-change="onCustomItemChange"
                @file-action="fileAction"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="detail-info">
        <div style="display: flex; justify-content: space-between">
          <div style="width: 50%">
            指标打分（系数）：{{
              itemDetail.result?.percent !== undefined && itemDetail.result?.percent !== null
                ? itemDetail.result.percent
                : '--'
            }}
          </div>
          <div style="width: 50%">
            复核打分（系数）：{{
              itemDetail.result?.checkPercent !== undefined && itemDetail.result?.checkPercent !== null
                ? itemDetail.result.checkPercent
                : '--'
            }}
          </div>
        </div>
        <div style="display: flex; justify-content: space-between">
          <div style="width: 50%">
            指标得分(审核评分)：{{
              itemDetail.result?.score !== undefined && itemDetail.result?.score !== null
                ? itemDetail.result.score
                : '--'
            }}
          </div>
          <div style="width: 50%">
            最终得分：{{
              itemDetail.result?.checkScore !== undefined && itemDetail.result?.checkScore !== null
                ? itemDetail.result.checkScore
                : '--'
            }}
          </div>
        </div>
        <div style="display: flex; justify-content: space-between">
          <div style="width: 50%">
            评审意见：{{
              itemDetail.result?.reviewAdvice !== undefined && itemDetail.result?.reviewAdvice !== null
                ? itemDetail.result?.reviewAdvice
                : '--'
            }}
          </div>
          <div style="width: 50%">
            复核意见：{{
              itemDetail.result?.checkAdvice !== undefined && itemDetail.result?.checkAdvice !== null
                ? itemDetail.result?.checkAdvice
                : '--'
            }}
          </div>
        </div>
        <div>评审凭证</div>
        <van-uploader
          capture="camera"
          accept="image/*"
          @delete="handleDelete"
          :deletable="false"
          disabled
          :max-count="5"
          v-model="itemDetail.result.cert"
        />
      </div>
    </div>
    <file-preview
      :fileId="fileId"
      :show.sync="filePreviewDialog"
      @cancel="filePreviewClose"
      @confirm="filePreviewConfirm"
    />
  </div>
</template>

<script>
import { download, getFileInfo, useDownload, upload, saveCustomResult } from '@/api/member-home/common'
import { Toast } from 'vant'
import TreeItem from '@/components/TreeItem.vue'
import FilePreview from '@/components/FilePreview.vue'

export default {
  name: 'CustomDetail',
  components: {
    TreeItem,
    FilePreview
  },
  props: {
    itemDetail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      filePreviewDialog: false,
      fileId: ''
    }
  },
  methods: {
    submitData() {
      if (
        this.itemDetail.result.percent &&
        this.itemDetail.result.percent >= 0 &&
        this.itemDetail.result.percent <= 100
      ) {
        return true
      } else {
        if (this.itemDetail.result.percent) {
          Toast.fail('系数不合法')
        } else {
          Toast.fail('请填写系数')
        }
      }
      return false
    },
    handleDelete(file, fileList) {
      console.log(file)
      if (file.isImage) {
        console.log('删除自blob转化而来的图片', file.index)
        const index = file.index
        // 删除时注意移除reviewCert中相应的id
        this.itemDetail.result.reviewCert.splice(index, 1)
        console.log(this.itemDetail.result.reviewCert)
      }
    },
    handleDownload(fileId) {
      console.log(fileId)
      getFileInfo(fileId).then(response => {
        console.log(response)
        const fileName = response.data.originalFileName
        const suffix = '.' + response.data.suffixName
        useDownload(download, fileName, { fileName: fileId }, suffix)
      })
    },
    filePreview(item) {
      console.log(item, 'preview')
      this.fileId = item.fileId
      this.filePreviewDialog = true
    },
    filePreviewClose() {
      this.filePreviewDialog = false
    },
    filePreviewConfirm() {
      this.filePreviewDialog = false
    },
    async fileAction(file, detail, item) {
      console.log(file, detail)
      try {
        const fileId = await this.uploadFile(file)
        item.fileId = fileId
        item.originalFileName = file.file.name
        saveCustomResult({ id: item.id, fileId: item.fileId })
          .then(res => {
            console.log(res)
            Toast.success('上传成功')
          })
          .catch(() => {})
      } catch (error) {
        console.error('上传失败', error)
        Toast.fail('上传失败')
      }
    },
    async uploadFile(fileData) {
      try {
        const formData = new FormData()
        formData.append('file', fileData.file)
        const res = await upload(formData)
        console.log('upload file', res)
        return res.data.fileId
      } catch (error) {
        console.error('upload error', error)
        return null
      }
    },
    onCustomItemChange(item, type) {
      console.log('Custom item changed:', item, type)
      switch (type) {
        case 'RADIO':
          console.log('radio changed', item.optSelected)
          saveCustomResult({ id: item.id, optSelected: item.optSelected })
            .then(res => {
              console.log(res)
            })
            .catch(() => {})
          break
        case 'INPUT':
        case 'TEXT':
          console.log('radio changed', item.textContent)
          saveCustomResult({ id: item.id, textContent: item.textContent })
            .then(res => {
              console.log(res)
            })
            .catch(() => {})
          break
        case 'ATTACHMENT':
          console.log('radio changed', item.fileId)
          saveCustomResult({ id: item.id, fileId: item.fileId })
            .then(res => {
              console.log(res)
            })
            .catch(() => {})
          break
        default:
          break
      }
    }
  }
}
</script>

<style scoped lang="scss">
.item-detail {
  flex: 1;
  width: 100%;
  min-height: 300px;
  padding: 10px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  display: flex;
  flex-direction: column;

  .info-text {
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }

  .dept-text {
    min-width: 62px;
    max-width: 400px;
    min-height: 32px;
    background: #666666;
    border-radius: 10px 10px 10px 10px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    padding: 6px;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    text-align: center;
  }

  .score-text {
    width: 62px;
    height: 32px;
    background: #daf3ff;
    border-radius: 10px 10px 10px 10px;
    font-weight: bold;
    font-size: 18px;
    color: #01a3f2;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
  }

  .disabled-text {
    height: 22px;
    line-height: 22px;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
    margin-left: 5px;
  }

  .detail-desc {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-weight: bold;
    font-size: 20px;
    color: #000000;
    overflow-y: auto;
  }

  .detail-info {
    margin-top: 10px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 10px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: bold;
    font-size: 16px;
    color: #000000;

    .uni-input {
      border-radius: 10px 10px 10px 10px;
    }

    .upload-btn {
      width: 99px;
      height: 40px;
      background: #ffffff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: 500;
      font-size: 16px;
      color: #01a3f2;
      line-height: 19px;
    }

    .result-content {
      background: #f5f5f5;
      border-radius: 10px 10px 10px 10px;
      font-weight: bold;
      font-size: 16px;
      color: #000000;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .uni-input {
        border-radius: 10px 10px 10px 10px;
      }
    }
  }

  .core-text {
    width: 104px;
    height: 32px;
    background: #eeeeee;
    border-radius: 10px 10px 10px 10px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }

  .rules-content {
    background: #effaff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #56bfee;
    margin-top: 10px;
    font-weight: 500;
    font-size: 14px;
    color: #000000;
    padding: 5px;
    line-height: 26px;
  }

  .detail-action {
    padding: 10px 10px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-btn {
      width: 120px;
      height: 40px;
      background: #ebf8ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: bold;
      font-size: 16px;
      color: #01a3f2;
      display: flex;
      align-items: center;
      justify-content: center;

      &.disabled {
        background: #f5f5f5;
        color: #d7d7d7;
        border: 0px solid #01a3f2;
      }
    }

    .review-btn {
      width: 120px;
      height: 40px;
      background: #01a3f2;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
