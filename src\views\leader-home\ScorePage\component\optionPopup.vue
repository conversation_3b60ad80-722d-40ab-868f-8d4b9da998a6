<template>
  <van-popup
    round
    v-model="dialogShow"
    position="bottom"
    class="resultPop"
    :style="{ width: '100%' }"
    @close="updateDialogShow(false)"
  >
    <div class="result-content">
      <div><span style="color: #ff3b30">*</span>指标复核</div>
      <div>
        <van-radio-group v-model="data.select">
          <div style="display: flex; flex-direction: column;">
            <div v-for="(item, index) in data.list" :key="index">
              <van-radio :name="item.id">
                <template>
                  <span class="option-name">
                    {{ item.label }}
                  </span>
                  <span class="option-desc">
                    {{ item.question }}
                  </span>
                </template>
              </van-radio>
            </div>
          </div>
        </van-radio-group>
      </div>
      <div>复核意见（非必填）</div>
      <div>
        <van-field
          class="uni-input"
          v-model="data.comments"
          placeholder="请填写本指标评审意见
"
        ></van-field>
      </div>
    </div>
    <div class="action-container">
      <div>
        <van-button type="default" class="cancel-btn" @click="onCancel">取消</van-button>
        <van-button type="default" class="confirm-btn" @click="onConfirm">确定</van-button>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { Toast } from 'vant'

export default {
  name: 'OptionPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    onConfirm() {
      if (this.data.select) {
        this.$emit('confirm')
        this.$emit('update:show', false)
      } else {
        Toast.fail('请选择')
      }
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.resultPop {
  background-color: #fff;
  padding: 10px;

  .result-content {
    padding: 10px;
    background: #f5f5f5;
    border-radius: 10px 10px 10px 10px;
    font-weight: bold;
    font-size: 16px;
    color: #000000;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .option-name {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 19px

    }

    .option-desc {
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      margin-left: 20px

    }


    .uni-input {
      border-radius: 10px 10px 10px 10px;
    }
  }

  .action-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    margin-top: 10px;

    .cancel-btn {
      width: 172px;
      height: 50px;
      background: #ffffff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #d7d7d7;
      font-weight: 500;
      font-size: 17px;
      color: #333333;
      margin-right: 10px;
    }

    .confirm-btn {
      width: 172px;
      height: 50px;
      background: #01a3f2;
      border-radius: 5px 5px 5px 5px;
      font-weight: 500;
      font-size: 17px;
      color: #ffffff;
    }
  }
}
</style>
