/**
 * 基础路由
 * @type { *[] }
 */
export const constantRouterMap = [
  {
    path: '/',
    redirect: '/leader-home'
  },
  {
    path: '/login',
    component: () => import('@/views/login/login'),
    name: '/login',
    meta: {
      title: '登录页面',
      keepAlive: false
    }
  },
  {
    path: '/leader-home',
    component: () => import('@/views/leader-home/index'),
    name: '/leader-home',
    meta: {
      title: '组长首页',
      keepAlive: false
    }
  },
  {
    path: '/leader-review',
    component: () => import('@/views/leader-home/ReviewPage/index'),
    name: '/leader-review',
    meta: {
      title: '指标页面',
      keepAlive: false
    }
  },
  {
    path: '/leader-item',
    component: () => import('@/views/leader-home/ItemPage/index'),
    name: '/leader-item',
    meta: {
      title: '复核页面',
      keepAlive: false
    }
  },
  {
    path: '/leader-detail',
    component: () => import('@/views/leader-home/DetailPage/index'),
    name: '/leader-item',
    meta: {
      title: '指标详情',
      keepAlive: false
    }
  },
  {
    path: '/leader-score',
    component: () => import('@/views/leader-home/ScorePage/index'),
    name: '/leader-score',
    meta: {
      title: '评分页面',
      keepAlive: false
    }
  },
  {
    path: '/leader-grouped',
    component: () => import('@/views/leader-home/GroupedPage/index'),
    name: '/leader-item',
    meta: {
      title: '分组页面',
      keepAlive: false
    }
  },
  // 废弃，现在两者共用首页
  {
    path: '/member-home',
    component: () => import('@/views/member-home/index'),
    name: '/member-home',
    meta: {
      title: '专家首页',
      keepAlive: false
    }
  },
  {
    path: '/member-not-started',
    component: () => import('@/views/member-home/NotStartedPage/index'),
    name: '/member-home',
    meta: {
      title: '未开始',
      keepAlive: false
    }
  },
  {
    path: '/member-in-review',
    component: () => import('@/views/member-home/InReviewPage/index'),
    name: '/member-home',
    meta: {
      title: '评审中',
      keepAlive: false
    }
  },
  {
    path: '/member-in-re-review',
    component: () => import('@/views/member-home/InReReviewPage/index'),
    name: '/member-home',
    meta: {
      title: '复核中',
      keepAlive: false
    }
  },
  {
    path: '/member-completed',
    component: () => import('@/views/member-home/CompletedPage/index'),
    name: '/member-home',
    meta: {
      title: '已完成',
      keepAlive: false
    }
  }
]
