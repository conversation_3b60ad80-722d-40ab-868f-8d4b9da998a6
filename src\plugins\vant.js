// 按需全局引入 vant组件
import Vue from 'vue'
import {
  Button,
  List,
  Cell,
  Tabbar,
  TabbarItem,
  Col,
  Row,
  Popup,
  Collapse,
  CollapseItem,
  Form,
  Field,
  Divider,
  Checkbox,
  CheckboxGroup,
  Image as VanImage,
  CellGroup,
  Toast,
  Tab,
  Tabs,
  Dialog,
  Icon,
  Switch,
  DropdownMenu,
  DropdownItem,
  Popover
} from 'vant'
Vue.use(Tab)
Vue.use(Tabs)
Vue.use(Dialog)
Vue.use(Collapse)
Vue.use(CollapseItem)
Vue.use(Toast)
Vue.use(VanImage)
Vue.use(Popup)
Vue.use(Divider)
Vue.use(Button)
Vue.use(Cell)
Vue.use(List)
Vue.use(Form)
Vue.use(Field)
Vue.use(Checkbox)
Vue.use(CheckboxGroup)
Vue.use(CellGroup)
Vue.use(Tabbar).use(TabbarItem)
Vue.use(Dialog)
Vue.use(Col)
Vue.use(Row)
Vue.use(Icon)
Vue.use(Switch)
Vue.use(DropdownMenu)
Vue.use(DropdownItem)
Vue.use(Popover)
