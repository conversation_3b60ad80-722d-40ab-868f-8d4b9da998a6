import request from '@/utils/request'

/**
 * 复核进度情况-定量
 * @param {string} data.taskId 评审任务id
 * @returns {Promise}
 */
export function getQuantProgress(data) {
  return request({
    url: `/new/assess/task/progress/group/progress`,
    method: 'post',
    data
  })
}

/**
 * 复核进度情况-现场
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function getSiteProgress(data) {
  return request({
    url: '/pad/review/check/list',
    method: 'post',
    data
  })
}

/**
 * 获取分值-现场
 * @param {string} data.expertSign 专家签字
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function getSiteCal(data) {
  return request({
    url: '/pad/review/compute/result',
    method: 'post',
    data
  })
}

/**
 * 获取分值结果-现场
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function getSiteResult(data) {
  return request({
    url: '/score/result/list',
    method: 'post',
    data
  })
}

/**
 * 提交复核结果-现场
 * @param {string} data.expertSign 专家签字
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function submitSiteReview(data) {
  return request({
    url: '/pad/review/all/finish',
    method: 'post',
    data
  })
}

/**
 * 获取分值-定量
 * @param {string} data.taskId 评审任务id
 * @returns {Promise}
 */
export function getQuantCal(taskId) {
  return request({
    url: `/new/assess/task/progress/cal/${taskId}`,
    method: 'get'
  })
}

/**
 * 提交复核结果-定量
 * @param {string} data.taskId 评审任务id
 * @param {string} data.signId 签名id
 * @returns {Promise}
 */
export function submitQuantReview(data) {
  return request({
    url: `/new/assess/task/progress/submit/task`,
    method: 'post',
    data
  })
}
