<template>
  <div class="container">
    <div class="login-container">
      <div class="head-band">
        <img src="../../assets/img/band.png" alt="band" class="band-img" />
      </div>
      <div class="head-title">
        <div class="title">潍坊市医院等级评审系统</div>
        <div class="info">评审专家入口</div>
      </div>

      <!-- 登录方式切换 -->
      <div class="login-type-switch">
        <div class="switch-item" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">
          账号密码登录
        </div>
        <div class="switch-item" :class="{ active: loginType === 'sms' }" @click="switchLoginType('sms')">
          手机验证码登录
        </div>
      </div>

      <div class="login-content">
        <van-form @submit="handleLogin" ref="loginForm" :model="loginForm">
          <!-- 账号密码登录 -->
          <template v-if="loginType === 'password'">
            <van-field left-icon="user" class="uni-input" ref="username" v-model="loginForm.username"
              placeholder="请输入账号">
            </van-field>
            <van-field left-icon="lock" class="uni-input" ref="password" v-model="loginForm.password" type="password"
              placeholder="请输入密码">
            </van-field>
          </template>

          <!-- 手机验证码登录 -->
          <template v-else>
            <van-field left-icon="phone" class="uni-input" ref="userPhone" v-model="loginForm.userPhone"
              placeholder="请输入手机号" maxlength="11" type="tel">
            </van-field>
            <div class="code-input-wrapper">
              <van-field left-icon="certificate" class="uni-input code-input" ref="validCode"
                v-model="loginForm.validCode" placeholder="请输入验证码" maxlength="6">
              </van-field>
              <van-button class="send-code-btn" :disabled="!canSendCode || countdown > 0" @click="sendSmsCode"
                size="small">
                {{ countdown > 0 ? `${countdown}s` : '发送验证码' }}
              </van-button>
            </div>
          </template>
        </van-form>
      </div>
      <div class="bottom">
        <van-button :disabled="notFill" native-type="submit" @click="handleLogin"
          :style="{ opacity: notFill ? '0.5' : '1' }" class="login-btn">
          登录
        </van-button>
      </div>
    </div>
    <div class="el-login-footer" :style="{ position: isKeyboardShow ? 'static' : 'absolute' }">
      <span>技术支持：许工&nbsp;&nbsp;&nbsp;&nbsp;联系电话：40060221203</span>
    </div>
  </div>
</template>

<script>
import { sendSmsCode } from '@/api/user'

export default {
  name: 'Login',
  data() {
    return {
      loginType: 'password', // 登录方式：password 账号密码，sms 短信验证码
      loginForm: {
        username: '',
        password: '',
        userPhone: '',
        validCode: ''
      },
      loading: false,
      notFill: true,
      isKeyboardShow: false,
      countdown: 0, // 验证码倒计时
      countdownTimer: null // 倒计时定时器
    }
  },
  computed: {
    // 是否可以发送验证码
    canSendCode() {
      const phoneReg = /^1[3-9]\d{9}$/
      return phoneReg.test(this.loginForm.userPhone)
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  watch: {
    loginForm: {
      handler(val, oval) {
        console.log(val)
        // 根据登录方式判断表单是否填写完整
        if (this.loginType === 'password') {
          this.notFill = !(val.password !== '' && val.username !== '')
        } else {
          this.notFill = !(val.userPhone !== '' && val.validCode !== '')
        }
      },
      deep: true
    },
    loginType() {
      // 切换登录方式时重新计算表单是否填写完整
      this.$nextTick(() => {
        if (this.loginType === 'password') {
          this.notFill = !(this.loginForm.password !== '' && this.loginForm.username !== '')
        } else {
          this.notFill = !(this.loginForm.userPhone !== '' && this.loginForm.validCode !== '')
        }
      })
    }
  },
  methods: {
    handleResize() {
      this.isKeyboardShow = window.innerHeight < window.outerHeight
    },

    // 切换登录方式
    switchLoginType(type) {
      this.loginType = type
      // 清空表单
      this.loginForm = {
        username: '',
        password: '',
        userPhone: '',
        validCode: ''
      }
      // 如果切换到账号密码登录，清除倒计时
      if (type === 'password' && this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdown = 0
      }
    },

    // 发送短信验证码
    async sendSmsCode() {
      if (!this.canSendCode) {
        this.$toast('请输入正确的手机号')
        return
      }

      try {
        // 调用发送验证码的API
        await sendSmsCode(this.loginForm.userPhone)

        this.$toast('验证码发送成功')

        // 开始倒计时
        this.countdown = 120
        this.countdownTimer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.countdownTimer)
            this.countdownTimer = null
          }
        }, 1000)
      } catch (error) {
        console.error('发送验证码失败:', error)
        this.$toast('验证码发送失败，请重试')
      }
    },

    handleLogin() {
      // 根据登录方式验证表单
      if (this.loginType === 'password') {
        if (!this.loginForm.username) {
          this.$toast('请输入账号')
          return
        }
        if (!this.loginForm.password) {
          this.$toast('请输入密码')
          return
        }
      } else {
        if (!this.canSendCode) {
          this.$toast('请输入正确的手机号')
          return
        }
        if (!this.loginForm.validCode) {
          this.$toast('请输入验证码')
          return
        }
      }

      this.loading = true
      this.$store.dispatch('Login', this.loginForm).then(response => {
        // 跳转至home页面，在路由导航中处理具体首页内容
        this.$router.push({
          path: '/home'
        })
        this.loading = false
      }).catch(error => {
        console.error('登录失败:', error)
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  width: 100%;
  background-image: url('../../assets/img/bg_img.png');
  background-size: cover;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: relative;
  padding: 20px 0;
}

.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 30px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 10px 0 #d6deff;
  border-radius: 20px 20px 20px 20px;
}

.head-band {
  margin-bottom: 20px;
}

.band-img {
  width: 70px;
  height: 70px;
}

.head-title {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #000000;
  margin-bottom: 20px;

  .title {
    height: 28px;
    font-weight: bold;
    font-size: 20px;
    color: #000000;
    line-height: 23px;
  }

  .info {
    height: 22px;
    font-weight: 500;
    font-size: 16px;
    color: #666666;
    line-height: 19px;
  }
}

// 登录方式切换
.login-type-switch {
  display: flex;
  width: 300px;
  margin-bottom: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  padding: 4px;

  .switch-item {
    flex: 1;
    text-align: center;
    padding: 8px 16px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;

    &.active {
      background: #124e7e;
      color: #fff;
      font-weight: 500;
    }

    &:hover:not(.active) {
      background: #e0e0e0;
    }
  }
}

.content-intro {
  margin: 0 0 3px calc((100vw - 342px) / 2);
  font-size: 15px;
  font-weight: 400;
  color: #485465;
  line-height: 23px;
}

.login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;

  .uni-input {
    width: 300px;
    font-size: 14px;
    height: 40px;
    background: #e9f6f9;
    border-radius: 10px 10px 10px 10px;
    margin-bottom: 10px;
  }

  // 验证码输入框容器
  .code-input-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;

    .code-input {
      width: 200px;
      margin-bottom: 0;
    }

    .send-code-btn {
      width: 90px;
      height: 40px;
      background: #124e7e;
      color: #fff;
      border: none;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.3s ease;

      &:disabled {
        background: #ccc;
        color: #999;
      }

      &:not(:disabled):hover {
        background: #0d3a5e;
      }
    }
  }
}

.bottom {
  display: flex;

  .login-btn {
    width: 300px;
    height: 40px;
    background: #124e7e;
    border-radius: 10px 10px 10px 10px;

    font-weight: bold;
    font-size: 16px;
    color: #ffffff;
    line-height: 19px;
  }
}

.el-login-footer {
  bottom: 30px;
  color: #666;
  font-size: 16px;
  margin-top: 20px;
}
</style>
