import request from '@/utils/request'
const prefix = process.env.VUE_APP_API_PREFIX

// 登录方法
export function login(loginData) {
  return request({
    url: prefix + '/login',
    method: 'post',
    headers: { 'Content-type': 'application/json;charset=UTF-8' },
    data: loginData
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: prefix + '/getInfo',
    method: 'get'
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: prefix + '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 获取当前用户详细信息
export function getExpInfo() {
  return request({
    url: '/xcjc/expert/cur',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: prefix + '/captchaImage',
    method: 'get'
  })
}

// 发送短信验证码
export function sendSmsCode(phone) {
  return request({
    url: prefix + '/sendSmsCode',
    method: 'post',
    headers: { 'Content-type': 'application/json;charset=UTF-8' },
    data: { phone }
  })
}

// // 刷新token
// // grant_type=refresh_token&refresh_token=91e7b76e-f3b4-491f-9c85-8e28a32957d3&client_id=kangyang&client_secret=123456
// export function refreshToken(refresh_token) {
//   return request({
//     url:
//       '/api/auth/oauth/token?grant_type=refresh_token' +
//       '&refresh_token=' +
//       refresh_token +
//       '&client_id=' +
//       client_id +
//       '&client_secret=' +
//       client_secret,
//     method: 'get'
//   })
// }
