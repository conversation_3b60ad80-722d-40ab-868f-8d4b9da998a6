<template>
  <div class="item-detail">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div style="display: flex; align-items: center">
        <div class="info-text">检查科室：</div>
        <div class="dept-text">{{ itemDetail.department ? itemDetail.department : '--' }}</div>
        <!-- <div class="info-text">指标分值：</div>
        <div class="score-text">{{ itemDetail.score ? itemDetail.score : '--' }}</div> -->
      </div>
    </div>
    <van-divider :style="{ color: '#EEEEEE' }"></van-divider>
    <div class="detail-desc">
      <div>
        <div>{{ itemDetail.name }}</div>
        <div v-if="itemDetail.core" class="core-text">★核心指标</div>
      </div>
      <div v-if="itemDetail.status === true">
        <div class="detail-info">
          <div class="result-content">
            <div><span style="color: #ff3b30">*</span>评审选项</div>
            <div>
              <van-radio-group :disabled="isSubmit" v-model="itemDetail.result.optionResult">
                <div style="display: flex; flex-direction: column;">
                  <div v-for="(item, index) in itemDetail.options" :key="index">
                    <van-radio :name="item.name">
                      <template>
                        <span class="option-name">
                          {{ item.name ? item.name : '--' }}
                        </span>
                        <span class="option-desc">
                          {{ item.des ? item.des : '--' }}
                        </span>
                      </template>
                    </van-radio>
                  </div>
                </div>
              </van-radio-group>
            </div>
            <div>评审意见（非必填）</div>
            <div>
              <van-field :disabled="isSubmit" class="uni-input" v-model="itemDetail.result.reviewAdvice" placeholder="请填写本指标评审意见
"></van-field>
            </div>
            <div>上传凭证（最多5张）</div>
            <van-uploader capture="camera" accept="image/*" :disabled="isSubmit" @delete="handleDelete" :max-count="5"
              v-model="itemDetail.result.cert" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'

export default {
  name: 'OptionDetail',
  props: {
    itemDetail: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isSubmit: {
      type: Boolean,
      default: false
    },
    isCheck: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    submitData() {
      if (this.itemDetail.result.optionResult) {
        return true
      } else {
        Toast.fail('请选择')
        return false
      }
    },
    handleDelete(file, fileList) {
      console.log(file)
      if (file.isImage) {
        console.log('删除自blob转化而来的图片', file.index)
        const index = file.index
        // 删除时注意移除reviewCert中相应的id
        this.itemDetail.result.reviewCert.splice(index, 1)
        console.log(this.itemDetail.result.reviewCert)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.item-detail {
  flex: 1;
  width: 100%;
  min-height: 300px;
  padding: 10px;
  background: #ffffff;
  border-radius: 10px 10px 0px 0px;
  display: flex;
  flex-direction: column;

  .info-text {
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }

  .dept-text {
    min-width: 62px;
    max-width: 400px;
    min-height: 32px;
    background: #666666;
    border-radius: 10px 10px 10px 10px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    padding: 6px;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    text-align: center;
  }

  .score-text {
    width: 62px;
    height: 32px;
    background: #daf3ff;
    border-radius: 10px 10px 10px 10px;
    font-weight: bold;
    font-size: 18px;
    color: #01a3f2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .disabled-text {
    height: 22px;
    line-height: 22px;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
    margin-left: 5px;
  }

  .detail-desc {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-weight: bold;
    font-size: 20px;
    color: #000000;
    overflow-y: auto;
  }

  .core-text {
    width: 104px;
    height: 32px;
    background: #eeeeee;
    border-radius: 10px 10px 10px 10px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }

  .result-content {
    background: #f5f5f5;
    border-radius: 10px 10px 10px 10px;
    font-weight: bold;
    font-size: 16px;
    color: #000000;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .option-name {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 19px
    }

    .option-desc {
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      margin-left: 20px
    }

    .uni-input {
      border-radius: 10px 10px 10px 10px;
    }
  }

  .detail-info {
    margin-top: 10px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 10px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: bold;
    font-size: 16px;
    color: #000000;
  }

  .detail-action {
    padding: 10px 10px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-btn {
      width: 120px;
      height: 40px;
      background: #ebf8ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: bold;
      font-size: 16px;
      color: #01a3f2;
      display: flex;
      align-items: center;
      justify-content: center;

      &.disabled {
        background: #f5f5f5;
        color: #d7d7d7;
        border: 0px solid #01a3f2;
      }
    }

    .review-btn {
      width: 120px;
      height: 40px;
      background: #01a3f2;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
