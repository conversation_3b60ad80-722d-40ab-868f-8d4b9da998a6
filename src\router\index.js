import Vue from 'vue'
import store from '../store'
import Router from 'vue-router'
import { constantRouterMap } from './router.config.js'
import { getToken } from '@/utils/auth' // get token from cookie

const whiteList = ['/login', '/auth-redirect', '/tech-serv'] // no redirect whitelist

// hack router push callback
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(Router)

const createRouter = () =>
  new Router({
    // mode: 'history', // 如果你是 history模式 需要配置vue.config.js publicPath
    // base: process.env.BASE_URL,
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRouterMap
  })

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

router.beforeEach((to, from, next) => {
  const version = '20250506'
  console.log('%c index.js||Line: 35 --> version: ', 'color:#0ff;', version)
  const hasToken = getToken()
  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else if (to.path === '/home') {
      // 对于首页跳转，一律请求接口获取用户信息
      store.dispatch('GetInfo').then(res => {
        const rolesVal = res.roles[0]
        const groupVal = res.expertGroups[0]
        console.log('GetInfo', res)
        console.log('rolesVal', rolesVal)
        console.log('groupVal', groupVal)
        if (rolesVal === 'yydjpsxt_hos_expert' && groupVal && groupVal.isLeader) {
          // 普通专家
          next({ path: '/leader-home' })
        } else {
          next({ path: '/leader-home' })
        }
      })
    } else if (store.getters.expName === '') {
      store.dispatch('GetInfo').then(res => {
        next()
      })
    } else {
      next()
    }
  } else {
    /* has no token*/
    console.log(to.path)
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login`)
    }
  }
})

export default router
