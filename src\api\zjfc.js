import request from '@/utils/request'

// 查询详情列表
export function listDetail(query) {
  return request({
    url: '/zjfcDetail/list',
    method: 'get',
    params: query
  })
}

// 查询详情列表
export function listHeader(query) {
  return request({
    url: '/zjfcHeader/list',
    method: 'get',
    params: query
  })
}

// 修改绑定专家
export function setZjfcExp(data) {
  return request({
    url: '/zjfcDetail/setZjfcExp',
    method: 'post',
    data: data
  })
}

// 查询填报模板详细
export function getZjfcItems(data) {
  return request({
    url: '/zjfcDetail/items',
    method: 'post',
    data: data
  })
}

// 查询填报模板详细
export function getZjfcCommitItems(data) {
  return request({
    url: '/zjfcDetail/commitItems',
    method: 'post',
    data: data
  })
}

// 批量更新
export function batchUpdateZjfcDetail(data) {
  return request({
    url: '/zjfcDetail/batchUpdate',
    method: 'post',
    data: data
  })
}

// 批量更新
export function batchMarkZjfcDetail(data) {
  return request({
    url: '/zjfcDetail/batchMark',
    method: 'post',
    data: data
  })
}

// 批量计算指标结果
export function expItemCal(data) {
  return request({
    url: '/zjfcDetail/expItemCal',
    method: 'post',
    data: data
  })
}

// 判断是否所有题都是已完成状态
export function ifItemFinish(query) {
  return request({
    url: '/zjfcDetail/isAllFinish',
    method: 'get',
    params: query
  })
}

// 专家进行提交操作 (同时进行算分操作)
export function expCommit(data) {
  return request({
    url: '/zjfcDetail/expCommit',
    method: 'post',
    data: data
  })
}

// 通过组长ID查询组内成员进度信息
export function getReviewProgress(data) {
  return request({
    url: '/zjfcDetail/reviewProgress',
    method: 'post',
    data: data
  })
}

// 通过组长ID查询组内成员进度信息
export function reject(data) {
  return request({
    url: '/zjfcDetail/reject',
    method: 'post',
    data: data
  })
}

// 通过组长ID查询组内成员进度信息
export function ifAllFinish(data) {
  return request({
    url: '/zjfcDetail/ifAllFinish',
    method: 'post',
    data: data
  })
}

// 修改记录
export function updateHeader(data) {
  return request({
    url: '/zjfcHeader',
    method: 'put',
    data: data
  })
}
