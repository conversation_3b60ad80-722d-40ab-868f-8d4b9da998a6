import Cookies from 'js-cookie'

const TokenKey = 'DJPS-PAD-Token'
const RefreshTokenKey = 'DJPS-PAD-Refresh-Token'
const ExchangeToken = 'DJPS-PAD-Exchange-Token'

/**
 * accessToken
 */
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  console.log(token)
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

/**
 * refreshoken
 */
export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey)
}

export function setRefreshToken(token) {
  return Cookies.set(RefreshTokenKey, token)
}

export function removeRefreshToken() {
  return Cookies.remove(RefreshTokenKey)
}

/**
 * get accessToken by refreshToken
 */

export function getExchangeToken() {
  return Cookies.get(ExchangeToken)
}

export function setExchangeTokenn() {
  return Cookies.get(ExchangeToken)
}
export function removeExchangeToken() {
  return Cookies.remove(ExchangeToken)
}
export function getAllCookies() {
  return Cookies.get()
}
