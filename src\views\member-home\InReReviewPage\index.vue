<template>
  <div class="page_container">
    <!-- 顶栏 20% 高度 -->
    <div class="top-bar">
      <nav-return :title="routerQuery.hosName" />
      <div class="nav-content">
        <van-tabs type="card" swipeable @click="handleNavClick" color="#124e7e">
          <van-tab :title-style="{ border: 'none', borderRadius: '10px' }" v-for="(item, index) in navList" :key="index"
            :title="item.name" :name="item.id" />
        </van-tabs>
      </div>
      <UserInfo />
    </div>

    <!-- 主体部分 -->
    <div class="main-container">
      <!-- 左侧侧边栏 20% 宽度 -->
      <div class="sidebar">
        <div class="sidebar-title">本组指标 {{ categoryList.filled }}/{{ categoryList.total }}</div>
        <div class="sidebar-title" style="display: flex; align-items: center;justify-content: center;">
          <van-search style="padding: 0;margin: 0;" v-model="searchValue" :clearable="false" shape="round"
            placeholder="请输入指标名称" @search="handleSearch" @cancel="handleReset" />
        </div>
        <div class="sidebar-menu">
          <div v-for="(item, index) in categoryList.list" :key="index" @click="handleCategoryClick(item)"
            class="menu-item" :class="{ active: selectCategoryId === item.id }">
            <span class="menu-item-text">
              <van-icon :color="selectCategoryId === item.id ? '#e7eaee' : '#ffffff'" name="checked" />

              {{ item.level }}
              {{ item.label }}</span>
          </div>
        </div>
        <div v-if="categoryList.total !== 0" class="sidebar-pagination">
          <van-pagination @change="handlePageChange" v-model="pageNum" :page-count="totalPage" mode="simple">
            <template #prev-text>
              <van-icon color="#124e7e" name="arrow-left" />
            </template>
            <template #next-text>
              <van-icon color="#124e7e" name="arrow" />
            </template>
            <template #page="{ text }">{{ text }}</template>
          </van-pagination>
        </div>
      </div>

      <!-- 右侧主要内容区域 -->
      <div class="main-content">
        <div class="main-content-title">评审结果已提交，请等待组长复核</div>
        <score-detail v-if="routerQuery.method === '打分' && selectNav?.title !== '定量指标'" :item-detail="itemDetail" />
        <option-detail v-if="routerQuery.method === '选项' && selectNav?.title !== '定量指标'" :item-detail="itemDetail" />
        <custom-detail v-if="routerQuery.method === '自定义' && selectNav?.title !== '定量指标'" :item-detail="itemDetail" />
        <quant-detail v-if="selectNav?.title === '定量指标'" :item-detail="itemDetail" />
      </div>
    </div>
  </div>
</template>

<script>
import { Toast } from 'vant'
import {
  download,
  getSiteGroupNotEmpty,
  getSiteResultList,
  getSiteProgress,
  getQuantResultList,
  getQuantProgress,
  getCustomList
} from '@/api/member-home/common'
import UserInfo from '@/components/userInfo'
import NavReturn from '@/components/navReturn'
import scoreDetail from './component/scoreDetail.vue'
import optionDetail from './component/optionDetail.vue'
import customDetail from './component/customDetail.vue'
import quantDetail from './component/quantDetail.vue'

export default {
  name: 'NotStartedPage',
  components: {
    UserInfo,
    NavReturn,
    scoreDetail,
    optionDetail,
    customDetail,
    quantDetail
  },
  data() {
    return {
      searchValue: '',
      userInfo: this.$store.state.user.userInfo,
      routerQuery: this.$route.query,
      navList: [],
      selectNav: null,
      categoryList: [],
      selectCategoryId: null,
      itemDetail: {},
      pageNum: 1,
      pageSize: 50,
      totalPage: 100
    }
  },
  created() {
    console.log(this.userInfo)
    this.fetchNavList()
  },
  computed: {
    currentIndex() {
      return this.categoryList.list?.findIndex(item => item.id === this.selectCategoryId)
    }
  },
  methods: {
    handleSearch() {
      console.log('searchValue', this.searchValue)
      if (this.selectNav.title === '定量指标') {
        this.fetchQuantResultList()
      } else {
        this.fetchSiteResultList()
      }
    },
    handleReset() {
      this.searchValue = ''
      if (this.selectNav.title === '定量指标') {
        this.fetchQuantResultList()
      } else {
        this.fetchSiteResultList()
      }
    },
    fetchNavList() {
      if (this.routerQuery.type === '现场') {
        getSiteGroupNotEmpty({
          templateId: this.routerQuery.templateId,
          checkGroupType: 'GROUPED',
          recordId: this.routerQuery.recordId,
          reviewType: this.routerQuery.reviewType,
          expertId: this.userInfo.user.userId
        }).then(res => {
          console.log(res)
          this.navList = res.data.map(item => {
            return { id: item.id, name: item.groupName }
          })
          console.log('是否关联定量:', this.routerQuery.configPlan)
          if (this.routerQuery.configPlan === 'true' && this.routerQuery.reviewType === 'INSPECT') {
            this.navList.push({ id: 12345678, name: '定量指标' })
          }
          this.handleNavClick(this.navList[0].id, this.navList[0].name)
        })
      } else {
        this.navList = [{ id: 12345678, name: '定量指标' }]
        this.handleNavClick(this.navList[0].id, this.navList[0].name)
      }
    },
    handleNavClick(name, title) {
      this.searchValue = ''
      console.log(name, title)
      this.selectNav = {
        id: name,
        title: title
      }
      if (this.selectNav.title === '定量指标') {
        this.fetchQuantResultList()
      } else {
        this.fetchSiteResultList()
      }
    },
    handlePageChange(page) {
      console.log(page)
      this.pageNum = page
      this.handleNavClick(this.selectNav.id, this.selectNav.title)
    },
    fetchSiteResultList() {
      Promise.all([
        getSiteResultList(
          {
            groupId: this.selectNav.id,
            recordId: this.routerQuery.recordId,
            templateId: this.routerQuery.templateId,
            reviewMode: this.routerQuery.reviewMode,
            searchValue: this.searchValue
          },
          {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        ),
        getSiteProgress({
          groupId: this.selectNav.id,
          recordId: this.routerQuery.recordId
        })
      ]).then(res => {
        if (res[0].rows.length === 0) {
          Toast.fail('无数据')
          this.categoryList = {
            total: 0,
            filled: 0,
            list: []
          }
          this.itemDetail = {}
          return
        }
        console.log(res)
        this.totalPage = Math.ceil(res[0].total / this.pageSize)
        this.categoryList = {
          total: res[1].data.count,
          filled: res[1].data.finishCount,
          isSubmit: res[1].data.submit,
          isCheck: res[1].data.checkSubmit,
          list: res[0].rows.map(item => {
            return {
              id: item.id,
              level: item.standardItemChapter,
              label: item.standardItemName,
              evalStatus: item.reviewFinish,
              status: true,
              department: item.tips,
              score: item.standardScore,
              selfScore: item.selfScore,
              name: item.standardItemChapter + ' ' + item.standardItemName,
              gjcsCjfsbm: item.gjcsCjfsbm,
              itemList: item.itemList,
              options: item.options,
              core: item.core,
              reviewRules: item.reviewRules,
              result: {
                score: item.score,
                percent: item.percent,
                reviewAdvice: item.reviewAdvice,
                checkPercent: item.checkPercent,
                checkScore: item.checkScore,
                checkAdvice: item.checkAdvice,
                optionCheckResult: item.optionCheckResult,
                optionResult: item.optionResult,
                cert: [],
                certTemp: [],
                reviewCert: item.reviewCert ? item.reviewCert : []
              },
              custom: null
            }
          })
        }
        console.log(this.categoryList)
        this.handleCategoryClick(this.categoryList.list[0])
      })
    },
    fetchQuantResultList() {
      Promise.all([
        getQuantResultList(
          {
            taskId: this.routerQuery.taskId,
            expertId: this.userInfo.user.userId,
            zbMc: this.searchValue
          },
          {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
        ),
        getQuantProgress({
          taskId: this.routerQuery.taskId,
          expertId: this.userInfo.user.userId
        })
      ]).then(res => {
        if (res[0].rows.length === 0) {
          Toast.fail('无数据')
          this.categoryList = {
            total: 0,
            filled: 0,
            list: []
          }
          this.itemDetail = {}
          return
        }
        console.log(res)
        this.totalPage = Math.ceil(res[0].total / this.pageSize)
        this.categoryList = {
          total: res[1].data[0].totalCnt,
          filled: res[1].data[0].cnt,
          isSubmit: res[1].data[0].totalCnt === res[1].data[0].expertSignCnt,
          isCheck: res[1].data[0].reviewFinished,
          list: res[0].rows.map(item => {
            return {
              id: item.item.id,
              level: item.zbBm,
              label: item.zbMc,
              evalStatus: item.item.evalStatus !== 'BEGIN',
              status: item.workStatus === 'DONE',
              department: null,
              score: item.score,
              selfScore: item.selfScore,
              name: item.zbBm + ' ' + item.zbMc,
              gjcsCjfsbm: item.gjcsCjfsbm,
              itemList: item.itemList,
              core: null,
              reviewRules: item.reviewRules,
              result: {
                score: item.item.score,
                percent: item.item.percent,
                reviewAdvice: item.item.reviewAdvice,
                checkPercent: item.item.checkPercent,
                checkScore: item.item.checkScore,
                checkAdvice: item.item.checkAdvice,
                cert: [],
                certTemp: [],
                reviewCert: item.item.reviewCert ? item.item.reviewCert : []
              }
            }
          })
        }
        console.log(this.categoryList)
        this.handleCategoryClick(this.categoryList.list[0])
      })
    },
    handleCategoryClick(item) {
      this.selectCategoryId = item.id
      this.itemDetail = this.categoryList.list.find(element => element.id === item.id)
      if (this.itemDetail.result.reviewCert.length > 0 && this.itemDetail.result.cert.length === 0) {
        // 调用封装的下载转换方法，下载并转换文件URL
        this.downloadAndConvertFiles(this.itemDetail.result.reviewCert).then(({ urls }) => {
          this.itemDetail.result.cert = urls.map((url, index) => ({ url, isImage: true, index: index }))
        })
      }
      if (!this.itemDetail.custom && this.selectNav.title !== '定量指标' && this.routerQuery.method === '自定义') {
        // getCustomList(780).then(res => {
        //   this.itemDetail.custom = res.data
        //   console.log('this.itemDetail.custom', this.itemDetail.custom)
        // })
        getCustomList(this.itemDetail.id).then(res => {
          this.itemDetail.custom = res.data
        })
      }
    },
    // 封装文件下载和URL转换
    async downloadAndConvertFiles(fileIds) {
      // this.cleanupFileUrls() // 清理之前的URL对象
      try {
        const blobs = await Promise.all(fileIds.map(fileId => download({ fileName: fileId })))
        const urls = blobs.map(blob => URL.createObjectURL(blob))
        // 保存引用以便后续清理
        this.fileBlobs = blobs
        this.fileUrls = urls

        return { blobs, urls }
      } catch (error) {
        console.error('文件下载失败:', error)
        throw error
      }
    },
    // 注销创建的URL对象
    cleanupFileUrls() {
      console.log('清理文件URL')
      if (this.fileUrls) {
        this.fileUrls.forEach(url => URL.revokeObjectURL(url))
        this.fileUrls = []
        this.fileBlobs = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-tabs__nav--card {
  border: none;
}

.page_container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f2f5f8;

  .top-bar {
    height: 60px;
    flex-shrink: 0;
    background: #ffffff;
    display: flex;
    align-items: center;

    .nav-content {
      flex: 1;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .sidebar {
      background: #ffffff;
      width: 200px;
      padding: 5px 0px 5px 5px;
      display: flex;
      flex-direction: column;

      .sidebar-title {
        height: 38px;
        background: #f5f5f5;
        border-radius: 20px 20px 20px 20px;
        margin-right: 10px;
        margin-left: 5px;
        margin-bottom: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 16px;
        color: #666666;
      }

      .sidebar-menu {
        overflow-y: auto;
        flex: 1;
        min-width: 150px;
        display: flex;
        flex-direction: column;
        gap: 5px;

        .menu-item {
          height: 38px;
          line-height: 38px;
          font-weight: 500;
          font-size: 16px;
          color: #191d23;
          text-align: left;
          padding: 0px 10px;

          .menu-item-text {
            width: 100%;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &.active {
            background: #e7eaee;
          }
        }
      }

      .sidebar-btn {
        margin-right: 10px;
        margin-left: 5px;
        height: 40px;
        background: #f5f5f5;
        border-radius: 5px 5px 5px 5px;
        font-weight: bold;
        font-size: 16px;
        color: #d7d7d7;
        line-height: 19px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
          background: #01a3f2;
          color: #ffffff;
        }
      }

      .sidebar-pagination {
        margin-right: 10px;
        margin-left: 5px;
        margin-bottom: 5px;
        height: 40px;
        background: #f5f5f5;
        border-radius: 5px 5px 5px 5px;
        font-weight: bold;
        font-size: 16px;
        color: #d7d7d7;
      }
    }

    .main-content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .main-content-title {
        height: 52px;
        background: rgba(255, 246, 182, 0.5);
        border-radius: 10px 10px 10px 10px;
        border: 1px solid #dfc501;
        padding: 10px;
        font-weight: 500;
        font-size: 16px;
        color: #000000;
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
