{"name": "djps_pad", "version": "2.1.0", "description": "A vue h5 template with Vant UI", "author": "<PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "serve:local": "vue-cli-service serve --mode dev-local", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "stage": "vue-cli-service build --mode staging", "lint": "vue-cli-service lint"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "amfe-flexible": "^2.2.1", "axios": "^0.19.2", "core-js": "^3.6.4", "dayjs": "^1.9.7", "js-cookie": "^2.2.1", "jsencrypt": "^3.0.0-rc.1", "lib-flexible": "^0.3.2", "lodash": "^4.17.15", "regenerator-runtime": "^0.13.5", "vant": "^2.10.2", "vue": "^2.6.11", "vue-pdf": "^4.3.0", "vue-router": "^3.2.0", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "postcss-pxtorem": "^5.1.1", "sass": "^1.79.5", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^3.8.0"}}