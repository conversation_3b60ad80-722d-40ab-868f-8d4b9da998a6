import request from '@/utils/request'

/**
 * 身份认证接口
 * @param {Object} data - 请求参数
 * @param {string} data.name - 姓名
 * @param {string} data.idCard - 身份证号
 * @param {string} data.phone - 手机号
 * @returns {Promise} 返回身份认证的Promise对象
 */
export function identityAuth(data) {
  return request({
    url: '/account/auth',
    method: 'post',
    data: data
  })
}

/**
 * 获取专家评审年份列表
 * @returns {Promise}
 */
export function getExpertReviewYears(data) {
  return request({
    url: '/pad/review/year/list',
    method: 'post',
    data: data
  })
}

/**
 * @param {string} data.reviewYear 评审年度
 */
export function getReviewListByYear(data) {
  return request({
    url: '/pad/review/record/list',
    method: 'post',
    data: data
  })
}

/**
 * 获取专家分组列表-现场
 * @param {string} data.recordId 医院评审记录ID
 * @param {string} data.reviewType 评审类型：SELF 现场自评；INSPECT 现场检查
 * @returns {Promise}
 */
export function getSiteGroupList(data) {
  return request({
    url: '/pad/review/group/list',
    method: 'post',
    data: data
  })
}

/**
 * 获取专家分组列表-定量
 * @param {string} data.taskId 评审任务id
 * @returns {Promise}
 */
export function getQuantGroupList(data) {
  return request({
    url: '/new/assess/task/progress/group/expert',
    method: 'post',
    data: data
  })
}
/**
 * 开启评审-现场
 * @param {string} data.recordId 医院评审记录ID
 * @param {string} data.reviewType 评审类型: SELF 现场自评；INSPECT 现场检查
 * @returns {Promise}
 */
export function startSiteReview(data) {
  return request({
    url: '/pad/review/start',
    method: 'post',
    data: data
  })
}

/**
 * 开启评审-定量
 * @param {string} data.taskId 评审任务id
 * @returns {Promise}
 */
export function startQuantReview(taskId) {
  return request({
    url: `/new/assess/task/progress/begin/${taskId}`,
    method: 'get'
  })
}

/**
 * 重启评审-现场
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function restartSiteReview(data) {
  return request({
    url: '/pad/review/restart',
    method: 'post',
    data: data
  })
}

/**
 * 重启评审-定量
 * @param {string} data.taskId 评审任务id
 * @returns {Promise}
 */
export function restartQuantReview(taskId) {
  return request({
    url: `/new/assess/task/progress/restart/${taskId}`,
    method: 'get'
  })
}
