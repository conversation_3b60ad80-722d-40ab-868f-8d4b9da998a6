import request from '@/utils/request'

// 获取专家--代号对应信息
export function getList(query) {
  return request({
    url: '/xcjc/hosExpChptMark/listAll',
    method: 'get',
    params: query
  })
}

export function getNavList() {
  return new Promise(resolve => {
    setTimeout(() => {
      const navList = [
        {
          id: 1,
          name: '临床组',
          title: '主任医师',
          group: 'A组'
        },
        {
          id: 2,
          name: '综合组',
          title: '副主任医师',
          group: 'B组'
        },
        {
          id: 3,
          name: '定量指标',
          title: '主治医师',
          group: 'C组'
        }
      ]
      resolve({
        code: 200,
        data: navList
      })
    }, 100)
  })
}
function generateCategory(id) {
  const code = `1.1.1.${Math.floor(id / 10)}.${id % 10}`
  const name = `指标${id}`
  return {
    id: id,
    code: code,
    name: `${name}医院指标数据和其他信息`
  }
}

export function getCategoryList() {
  return new Promise(resolve => {
    setTimeout(() => {
      const quotaList = {
        total: 100,
        filled: 70,
        list: Array.from({ length: 40 }, (_, i) => generateCategory(i + 1))
      }
      resolve({
        code: 200,
        data: quotaList
      })
    }, 100)
  })
}

export function getItemDetail(code) {
  return new Promise(resolve => {
    setTimeout(() => {
      const itemDetail = {
        status: true,
        department: '心内科',
        score: 10,
        name: code + '医院的功能、任务和定位明确，规模适度，符合三级医院设置标准。',
        coefficient: 9,
        grade: 90,
        reviewOpinion: '指标完成情况良好',
        reviewVoucher: [
          'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
          'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
          'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg'
        ]
      }
      resolve({
        code: 200,
        data: itemDetail
      })
    }, 100)
  })
}
