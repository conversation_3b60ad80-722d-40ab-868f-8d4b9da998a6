<template>
  <div class="item-detail">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div style="display: flex; align-items: center">
        <div class="info-text">检查科室：</div>
        <div class="dept-text">{{ itemDetail.department ? itemDetail.department : '--' }}</div>
        <div class="info-text">指标分值：</div>
        <div class="score-text">{{ itemDetail.score ? itemDetail.score : '--' }}</div>
        <div v-if="itemDetail.selfScore" class="info-text">院内自评分数：</div>
        <div v-if="itemDetail.selfScore" class="score-text">{{ itemDetail.selfScore ? itemDetail.selfScore : '--' }}
        </div>
      </div>
    </div>
    <van-divider :style="{ color: '#EEEEEE' }"></van-divider>
    <div v-if="itemDetail.status === true" class="detail-desc">
      <div>{{ itemDetail.name }}</div>
      <div class="detail-info">
        <div style="display: flex; justify-content: space-between;">
          <div style="width: 50%">
            指标打分（系数）：{{ itemDetail.result?.percent !== undefined && itemDetail.result?.percent !== null ?
            itemDetail.result.percent : '--' }}
          </div>
          <div style="width: 50%">
            复核打分（系数）：{{ itemDetail.result?.checkPercent !== undefined && itemDetail.result?.checkPercent !== null ?
            itemDetail.result.checkPercent : '--' }}
          </div>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <div style="width: 50%">指标得分(审核评分)：{{ itemDetail.result?.score !== undefined && itemDetail.result?.score !==
            null ?
            itemDetail.result.score : '--' }}</div>
          <div style="width: 50%">
            最终得分：{{ itemDetail.result?.checkScore !== undefined && itemDetail.result?.checkScore !== null ?
            itemDetail.result.checkScore : '--' }}
          </div>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <div style="width: 50%">
            评审意见：{{ itemDetail.result?.reviewAdvice !== undefined && itemDetail.result?.reviewAdvice !== null ?
            itemDetail.result?.reviewAdvice : '--' }}
          </div>
          <div style="width: 50%">
            复核意见：{{ itemDetail.result?.checkAdvice !== undefined && itemDetail.result?.checkAdvice !== null ?
            itemDetail.result?.checkAdvice : '--' }}
          </div>
        </div>
        <div>评审凭证</div>
        <van-uploader capture="camera" accept="image/*" :deletable="false" disabled :max-count="5"
          v-model="itemDetail.result.cert" />
      </div>
    </div>
    <!-- 图片预览 -->
    <van-image-preview closeable v-model="imgShow" :images="imgList" :startPosition="imgIndex"
      @change="handleImgChange">
    </van-image-preview>
  </div>
</template>

<script>
export default {
  name: 'ScoreDetail',
  props: {
    itemDetail: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      imgIndex: 0,
      imgShow: false,
      imgList: []
    }
  },
  methods: {
    priviewImg(index) {
      console.log(index)
      this.imgIndex = index
      this.imgShow = true
      this.imgList = this.itemDetail.result.cert
    },
    handleImgChange(index) {
      this.imgIndex = index
    }
  }
}
</script>

<style scoped lang="scss">
.item-detail {
  flex: 1;
  width: 100%;
  min-height: 300px;
  padding: 10px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  display: flex;
  flex-direction: column;

  .info-text {
    font-weight: 500;
    font-size: 16px;
    color: #000000;
  }

  .dept-text {
    min-width: 62px;
    max-width: 400px;
    min-height: 32px;
    background: #666666;
    border-radius: 10px 10px 10px 10px;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    padding: 6px;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    text-align: center;
  }

  .score-text {
    width: 62px;
    height: 32px;
    background: #daf3ff;
    border-radius: 10px 10px 10px 10px;
    font-weight: bold;
    font-size: 18px;
    color: #01a3f2;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
  }

  .disabled-text {
    height: 22px;
    line-height: 22px;
    font-weight: 500;
    font-size: 16px;
    color: #000000;
    margin-left: 5px;
  }

  .detail-desc {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-weight: bold;
    font-size: 20px;
    color: #000000;
    overflow-y: auto;
  }

  .detail-info {
    padding: 10px;
    background: #f5f5f5;
    border-radius: 10px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-weight: bold;
    font-size: 16px;
    color: #000000;
  }

  .detail-action {
    padding: 10px 10px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-btn {
      width: 120px;
      height: 40px;
      background: #ebf8ff;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: bold;
      font-size: 16px;
      color: #01a3f2;
      display: flex;
      align-items: center;
      justify-content: center;

      &.disabled {
        background: #f5f5f5;
        color: #d7d7d7;
        border: 0px solid #01a3f2;
      }
    }

    .review-btn {
      width: 120px;
      height: 40px;
      background: #01a3f2;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #01a3f2;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
