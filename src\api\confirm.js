import request from '@/utils/request'

// 获取专家--代号对应信息
export function getList(query) {
  return request({
    url: '/xcjc/hosExpTemplateSymbol/list',
    method: 'get',
    params: query
  })
}

// 修改代号对应专家
export function updateExpSymbol(data) {
  return request({
    url: '/xcjc/hosExpTemplateSymbol',
    method: 'put',
    data: data
  })
}

// 根据专家组id获取成员列表
export function getMembers(id) {
  return request({
    url: '/xcjc/expertGroup/mems/' + id,
    method: 'get'
  })
}

// 修改部门
export function setExp(data) {
  return request({
    url: '/xcjc/hosExpTemplateSymbol',
    method: 'post',
    data: data
  })
}

// 修改记录
export function updateRecord(data) {
  return request({
    url: '/xcjc/hosExpAssignRecord',
    method: 'put',
    data: data
  })
}

// 查询部门详细
export function setAssign(data) {
  return request({
    url: '/xcjc/hosExpAssess/setAssign',
    method: 'post',
    data: data
  })
}
