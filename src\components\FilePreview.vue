<template>
  <van-dialog
    :value="dialogShow"
    :show-confirm-button="false"
    :show-cancel-button="false"
    :width="1000"
    title="文件预览"
    @confirm="onConfirm"
    @cancel="onCancel"
    @update:value="updateDialogShow"
  >
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span style="font-weight: bold; font-size: 18px; color: #333333">文件预览</span>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" width="24px" height="24px" alt="close" />
      </div>
    </template>
    <div class="pdfNoPage">
      <div v-if="isLoading" class="mask">
        <loading size="24px" vertical>加载中...</loading>
      </div>
      <div class="pdf-container">
        <div class="pdf-box">
          <pdf v-for="i in numPages" :key="i" :src="pdfSrc" :page="i" @page-loaded="pageLoaded" />
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import pdf from 'vue-pdf'
import { Loading } from 'vant'
import { download } from '@/api/member-home/common'
export default {
  metaInfo: {
    meta: [
      { charset: 'utf-8' },
      {
        name: 'viewport',
        content: 'width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=2,user-scalable=yes'
      }
    ]
  },
  // 名字
  name: 'FilePreview',
  // 部件
  components: {
    pdf,
    Loading
  },
  // 静态
  props: {
    show: {
      type: Boolean,
      default: false
    },
    fileId: {
      type: String,
      default: ''
    }
  },
  // 数据
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show,
      numPages: undefined,
      isLoading: true,
      pdfSrc: null
    }
  },
  // 属性的结果会被缓存，除非依赖的响应式属性变化才会重新计算。主要当作属性来使用；
  computed: {},
  // 对象内部的属性监听，也叫深度监听
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    },
    fileId: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.loadPdfFile()
        }
      }
    }
  },
  // 请求数据
  created() {},
  mounted() {},
  // 方法表示一个具体的操作，主要书写业务逻辑；
  methods: {
    async loadPdfFile() {
      this.isLoading = true
      try {
        // 下载文件
        const response = await download({ fileName: this.fileId })
        const blob = new Blob([response], { type: 'application/pdf' })
        const pdfUrl = URL.createObjectURL(blob)
        // 设置PDF源并获取页数
        this.pdfSrc = pdfUrl
        this.getNumPages(pdfUrl)
      } catch (error) {
        console.error('加载PDF文件失败:', error)
        this.isLoading = false
      }
    },
    getNumPages(url) {
      try {
        var loadingTask = pdf.createLoadingTask(url)
        loadingTask.promise
          .then(pdf => {
            this.url = loadingTask
            this.numPages = pdf.numPages
          })
          .catch(err => {
            console.log(err)
            this.isLoading = false
          })
      } catch (error) {
        console.error('获取PDF页数失败:', error)
        this.isLoading = false
      }
    },
    // 等pdf页数加载完成的时候，隐藏加载框
    pageLoaded(num) {
      if (num === this.numPages) {
        this.isLoading = false
      }
    },
    onConfirm() {
      this.$emit('confirm')
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}
::v-deep .van-dialog__content {
  padding: 0px 22px 22px;
}
// 加载框
.mask {
  width: 100%;
  height: 100%;
  position: relative;

  .van-loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.pdf-container {
  max-height: 70vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.pdf-box {
  padding: 10px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  overflow-x: hidden;
  font-size: 0.28rem;

  span {
    width: 100%;
  }

  ::v-deep .vuepdfiframe {
    width: 100% !important;
  }

  ::v-deep canvas {
    max-width: 100% !important;
    height: auto !important;
  }
}
</style>
