const state = {
  isLeader: '',
  assignId: '',
  templateId: '',
  standardId: '',
  expGroupId: '',
  hosId: '',
  hosName: '',
  assignDate: '',
  finishDate: '',
  selfMark: '',
  checkHosCode: '',
  applyLevel: ''
}
const mutations = {
  SET_ISLEADER(state, data) {
    state.isLeader = data
  },
  SET_ASSIGNID(state, data) {
    state.assignId = data
  },
  SET_TEMPLATEID(state, data) {
    state.templateId = data
  },
  SET_STANDARDID(state, data) {
    state.standardId = data
  },
  SET_EXPGROUPID(state, data) {
    state.expGroupId = data
  },
  SET_HOSID(state, data) {
    state.hosId = data
  },
  SET_HOSNAME(state, data) {
    state.hosName = data
  },
  SET_ASSIGNDATE(state, data) {
    state.assignDate = data
  },
  SET_FINISHDATE(state, data) {
    state.finishDate = data
  },
  SET_SELFMARK(state, data) {
    state.selfMark = data
  },
  SET_CHECKHOSCODE(state, data) {
    state.checkHosCode = data
  },
  SET_APPLYLEVEL(state, data) {
    state.applyLevel = data
  }
}
const actions = {
  setIsLeader({ commit }, data) {
    commit('SET_ISLEADER', data)
  },
  setAssignId({ commit }, data) {
    commit('SET_ASSIGNID', data)
  },
  setTemplateId({ commit }, data) {
    commit('SET_TEMPLATEID', data)
  },
  setStandardId({ commit }, data) {
    commit('SET_STANDARDID', data)
  },
  setExpGroupId({ commit }, data) {
    commit('SET_EXPGROUPID', data)
  },
  setHosId({ commit }, data) {
    commit('SET_HOSID', data)
  },
  setHosName({ commit }, data) {
    commit('SET_HOSNAME', data)
  },
  setAssignDate({ commit }, data) {
    commit('SET_ASSIGNDATE', data)
  },
  setFinishDate({ commit }, data) {
    commit('SET_FINISHDATE', data)
  },
  setSelfMark({ commit }, data) {
    commit('SET_SELFMARK', data)
  },
  setCheckHosCode({ commit }, data) {
    commit('SET_CHECKHOSCODE', data)
  },
  setApplyLevel({ commit }, data) {
    commit('SET_APPLYLEVEL', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
