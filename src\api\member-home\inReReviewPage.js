import request from '@/utils/request'

/**
 * 获取方案分组列表-现场
 * @param {Object} data 请求参数
 * @param {string} data.standardId 方案标准id
 * @param {string} data.templateId 分组模板id
 * @param {string} data.checkGroupType 分组类型
 * Enum:
 *   Array [ ALL, GROUPED, UNGROUP ]
 * @returns {Promise} 包含方案分组列表的Promise
 */
export function getSiteGroup(data) {
  return request({
    url: '/new/xcjc/checkGroup/group',
    method: 'post',
    data
  })
}

/**
 * 获取评审列表-现场
 * @param {Object} data 请求参数
 * @param {integer} data.expertId 专家ID
 * @param {integer} data.groupId 分组ID
 * @param {string} data.recordId 医院评审记录ID
 * @param {string} data.reviewMode 评审模式
 * Enum:
 *   Array [ 4 ] [ CUSTOM, NONE, OPTION, SCORE ]
 * @param {string} data.reviewType 评审类型：SELF 现场自评；INSPECT 现场检查
 * Enum:
 *   Array [ 2 ] [ INSPECT, SELF ]
 * @param {string} data.templateId 模版ID
 * @returns {Promise} 包含评审结果列表的Promise
 */
export function getSiteResultList(data, params) {
  return request({
    url: '/pad/review/result/list',
    method: 'post',
    data,
    params
  })
}

/**
 * 获取指标详情
 * @param {Object} data 请求参数
 * @param {integer} data.groupId 分组ID
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function getSiteProgress(data) {
  return request({
    url: '/pad/review/group/metric/info',
    method: 'post',
    data
  })
}

/**
 * 获取评审列表-定量
 * @param {Object} data 请求参数
 * @param {string} data.taskId 任务id
 * @returns {Promise} 包含定量评审结果分配列表的Promise
 */
export function getQuantResultList(data, params) {
  return request({
    url: '/new/assess/task/progress/score/detail/list',
    method: 'post',
    data,
    params
  })
}

/**
 * 复核进度情况-定量
 * @param {string} data.taskId 评审任务id
 * @returns {Promise}
 */
export function getQuantProgress(data) {
  return request({
    url: `/new/assess/task/progress/group/progress`,
    method: 'post',
    data
  })
}

/**
 * 提交打分-现场
 * @param {Object} data 请求参数
 * @returns {Promise} 包含提交结果的Promise
 */
export function submitSiteResult(data) {
  return request({
    url: '/pad/review/submit/result',
    method: 'post',
    data
  })
}

/**
 * 提交打分-定量
 * @param {Object} data
 * @param {integer} data.id  指标id
 * @param {number} data.percent  分数
 * @param {string} data.reviewAdvice 评审意见
 * @param {array} data.reviewCert 评审附件
 * @returns {Promise}
 */
export function submitQuantResult(data) {
  return request({
    url: '/new/assess/task/progress/save/score',
    method: 'post',
    data
  })
}

/**
 * 提交全部-定量
 * @param {Object} data
 * @param {string} data.signId  签名id
 * @param {integer} data.taskId  任务id
 * @returns {Promise}
 */
export function submitQuantTask(data) {
  return request({
    url: '/new/assess/task/progress/submit/task',
    method: 'post',
    data
  })
}

function generateCategory(id) {
  const code = `1.1.1.${Math.floor(id / 10)}.${id % 10}`
  const name = `指标${id}`
  return {
    id: id,
    code: code,
    name: `${name}医院指标数据和其他信息`
  }
}

export function getCategoryList() {
  return new Promise(resolve => {
    setTimeout(() => {
      const quotaList = {
        total: 100,
        filled: 70,
        list: Array.from({ length: 40 }, (_, i) => generateCategory(i + 1))
      }
      resolve({
        code: 200,
        data: quotaList
      })
    }, 100)
  })
}

export function getItemDetail(code) {
  return new Promise(resolve => {
    setTimeout(() => {
      const itemDetail = {
        status: true,
        department: '心内科',
        score: 10,
        name: code + '医院的功能、任务和定位明确，规模适度，符合三级医院设置标准。',
        coefficient: 9,
        grade: 90,
        reviewOpinion: '指标完成情况良好',
        reviewVoucher: [
          'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
          'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
          'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
          'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg'
        ]
      }
      resolve({
        code: 200,
        data: itemDetail
      })
    }, 100)
  })
}
