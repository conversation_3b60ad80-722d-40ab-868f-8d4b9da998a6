import request from '@/utils/request'

/**
 * 获取方案分组列表-现场，定量不存在分组
 * @param {Object} data 请求参数
 * @param {string} data.standardId 方案标准id
 * @param {string} data.templateId 分组模板id
 * @param {string} data.checkGroupType 分组类型
 * Enum:
 *   Array [ ALL, GROUPED, UNGROUP ]
 * @returns {Promise} 包含方案分组列表的Promise
 */
export function getSiteGroup(data) {
  return request({
    url: '/new/xcjc/checkGroup/group',
    method: 'post',
    data
  })
}

/**
 * 获取方案分组列表-现场，定量不存在分组, 不包含空分组
 * @param {Object} data 请求参数
 * @param {string} data.standardId 方案标准id
 * @param {string} data.templateId 分组模板id
 * @param {string} data.checkGroupType 分组类型
 * @param {string} data.recordId 医院评审记录ID
 * @param {string} data.reviewType 评审类型：SELF 现场自评；INSPECT 现场检查
 * Enum:
 *   Array [ ALL, GROUPED, UNGROUP ]
 * @returns {Promise} 包含方案分组列表的Promise
 */
export function getSiteGroupNotEmpty(data) {
  return request({
    url: '/pad/review/check/group',
    method: 'post',
    data
  })
}

/**
 * 获取评审列表-现场
 * @param {Object} data 请求参数
 * @param {integer} data.expertId 专家ID
 * @param {integer} data.groupId 分组ID
 * @param {string} data.recordId 医院评审记录ID
 * @param {string} data.reviewMode 评审模式
 * Enum:
 *   Array [ 4 ] [ CUSTOM, NONE, OPTION, SCORE ]
 * @param {string} data.reviewType 评审类型：SELF 现场自评；INSPECT 现场检查
 * Enum:
 *   Array [ 2 ] [ INSPECT, SELF ]
 * @param {string} data.templateId 模版ID
 * @returns {Promise} 包含评审结果列表的Promise
 */
export function getSiteResultList(data, params) {
  return request({
    url: '/pad/review/result/list',
    method: 'post',
    data,
    params
  })
}

/**
 * 获取评审列表-定量
 * @param {Object} data 请求参数
 * @param {integer} data.expertId 专家ID
 * @param {string} data.taskId 任务id
 * @returns {Promise} 包含定量评审结果分配列表的Promise
 */
export function getQuantResultList(data, params, hideloading = false) {
  return request({
    url: '/new/assess/task/progress/score/detail/list',
    method: 'post',
    data,
    params,
    hideloading
  })
}

/**
 * 进一步获取自定义结果详情-现场
 * @param {Object} id 请求参数
 */
export function getCustomList(id) {
  return request({
    url: `/new/xcjc/hosExpAssignResultDetail/${id}`,
    method: 'get',
    hideloading: true // 添加这一行
  })
}

/**
 * 进度情况-现场
 * @param {Object} data 请求参数
 * @param {integer} data.groupId 分组ID
 * @param {string} data.recordId 医院评审记录ID
 * @returns {Promise}
 */
export function getSiteProgress(data) {
  return request({
    url: '/pad/review/group/metric/info',
    method: 'post',
    data
  })
}

/**
 * 进度情况-定量
 * @param {string} data.taskId 评审任务id
 * @param {string} data.expertId 用户id
 * @returns {Promise}
 */
export function getQuantProgress(data) {
  return request({
    url: `/new/assess/task/progress/group/progress`,
    method: 'post',
    data
  })
}

/**
 * 提交打分-现场
 * @param {Object} data 请求参数
 * @param {integer} data.expertId 专家ID
 * @param {integer} data.id 评审结果ID
 * @param {string} data.optionResult 选项结果
 * @param {number} data.percent 打分百分比
 * @param {string} data.recordId 评审记录id
 * @param {string} data.reviewAdvice 评审意见
 * @param {array} data.reviewCert  评审凭证
 * @param {string} data.submitType 提交类型：REVIEW 审核；CHECK 复核
 * @returns {Promise} 包含提交结果的Promise
 */
export function submitSiteResult(data) {
  return request({
    url: '/pad/review/submit/result',
    method: 'post',
    data
  })
}

/**
 * 提交打分-现场-组长
 * @param {Object} data 请求参数
 * @param {integer} data.expertId 专家ID
 * @param {integer} data.id 评审结果ID
 * @param {string} data.optionResult 选项结果
 * @param {number} data.percent 打分百分比
 * @param {string} data.recordId 评审记录id
 * @param {string} data.reviewAdvice 评审意见
 * @param {array} data.reviewCert  评审凭证
 * @param {string} data.submitType 提交类型：REVIEW 审核；CHECK 复核 REVIEW_CHECK：审核并复核
 * @returns {Promise} 包含提交结果的Promise
 */
export function submitSiteResultLeader(data) {
  return request({
    url: '/pad/review/submit/result',
    method: 'post',
    data
  })
}

/**
 * 保存自定义结果-现场
 * @param {Object} data
 * @returns {Promise}
 */
export function saveCustomResult(data) {
  return request({
    url: '/new/xcjc/hosExpAssignResultDetail/save',
    method: 'post',
    data,
    hideloading: true // 添加这一行
  })
}

/**
 * 提交打分-定量
 * @param {Object} data
 * @param {integer} data.id  指标id
 * @param {number} data.percent  分数
 * @param {string} data.reviewAdvice 评审意见
 * @param {array} data.reviewCert 评审附件
 * @returns {Promise}
 */
export function submitQuantResult(data) {
  return request({
    url: '/new/assess/task/progress/save/score',
    method: 'post',
    data
  })
}

/**
 * 提交打分-定量-组长
 * @param {Object} data
 * @param {integer} data.id  指标id
 * @param {number} data.percent  分数
 * @param {string} data.reviewAdvice 评审意见
 * @param {array} data.reviewCert 评审附件
 * @returns {Promise}
 */
export function submitQuantResultLeader(data) {
  return request({
    url: '/new/assess/task/progress/save/score',
    method: 'post',
    data
  })
}

/**
 * 提交全部-现场
 * @param {Object} data
 * @param {string} data.recordId 评审记录id
 * @param {string} data.expertSign 专家签字
 * @param {string} data.submitType 提交类型：REVIEW 审核；CHECK 复核
 * @param {number} data.groupId 分组ID
 * @param {boolean} data.quantity 是否是定量提交
 * @returns {Promise}
 */
export function submitSiteTask(data) {
  return request({
    url: '/pad/review/finish',
    method: 'post',
    data
  })
}

/**
 * 提交全部-现场-组长
 * @param {Object} data
 * @param {string} data.recordId 评审记录id
 * @param {string} data.expertSign 专家签字
 * @param {string} data.submitType 提交类型：REVIEW 审核；CHECK 复核 REVIEW_CHECK：审核并复核
 * @param {number} data.groupId 分组ID
 * @param {boolean} data.quantity 是否是定量提交
 * @returns {Promise}
 */
export function submitSiteTaskLeader(data) {
  return request({
    url: '/pad/review/finish',
    method: 'post',
    data
  })
}

/**
 * 提交全部-定量
 * @param {Object} data
 * @param {string} data.signId  签名id
 * @param {integer} data.taskId  任务id
 * @returns {Promise}
 */
export function submitQuantTask(data) {
  return request({
    url: '/new/assess/task/progress/submit/task',
    method: 'post',
    data
  })
}

/**
 * 提交全部-定量-组长
 * @param {Object} data
 * @param {string} data.signId  签名id
 * @param {integer} data.taskId  任务id
 * @returns {Promise}
 */
export function submitQuantTaskLeader(data) {
  return request({
    url: '/new/assess/task/progress/submit/review/task',
    method: 'post',
    data
  })
}

/**
 * 提交全部-复核-定量
 * @param {Object} data
 * @param {string} data.expertId  用户id
 * @param {integer} data.taskId  任务id
 * @returns {Promise}
 */
export function submitQuantCheckTask(data) {
  return request({
    url: '/new/assess/task/progress/review/finish',
    method: 'post',
    data
  })
}

/**
 * 查询专家组成员
 * @param {integer} data.groupId - 专家组id
 */
export function listExperts(groupId) {
  return request({
    url: `/new/xcjc/export/group/${groupId}`,
    method: 'get'
  })
}

/**
 * 查询专家组成员
 * @param {Object} data
 * @param {integer} data.groupId - 专家组id
 */
export function listExpertsInside(groupId) {
  return request({
    url: `/new/hos/inside/export/group/${groupId}`,
    method: 'get'
  })
}

/**
 * 获取现场指标列表用于再分配
 * @param {Object} data 请求参数
 * @param {string} data.groupId 专家组ID
 * @param {string} data.recordId 评审记录ID
 * @param {string} data.reviewMode 评审模式 OPTION/SCORE/CUSTOM/NONE
 * @param {string} data.templateId 模板ID
 * @param {string} data.reviewType 评审类型 INSPECT/SELF
 * @returns {Promise} 包含指标列表的Promise
 */
export function getSiteResultListV2(data, params, hideloading = false) {
  return request({
    url: '/pad/review/expert/list',
    method: 'post',
    data,
    params,
    hideloading
  })
}

/**
 * 评审指标重新分配
 * @param {Object} data - 请求参数
 * @param {integer} data.expertId - 专家ID
 * @param {integer} data.assignId - 分配ID
 * @param {array<string>} data.standardItemIds - 指标ID列表
 * @returns {Promise} 返回分配结果的Promise对象
 */
export function siteAssignExpert(data) {
  return request({
    url: '/pad/review/assign/expert',
    method: 'post',
    data
  })
}

/**
 * 评审指标重新分配
 * @param {Object} data - 请求参数
 * @param {string} data.taskId - 任务ID
 * @param {string} data.userId - 用户ID
 * @param {array} data.list - 指标列表
 * @param {number} data.list[].id - 指标ID
 * @returns {Promise} 返回分配结果的Promise对象
 */
export function quantAssignExpert(data) {
  return request({
    url: '/new/assess/task/progress/detail/reassign',
    method: 'post',
    data
  })
}

/**
 * 文件下载接口
 * @param {Object} params - 请求参数
 * @param {string} params.fileName - 要下载的文件名
 * @returns {Promise} 返回文件下载的Promise对象
 */
export function download(params) {
  return request({
    url: '/common/download',
    method: 'get',
    params: params,
    responseType: 'blob',
    hideloading: true // 添加这一行
  })
}

/**
 * 文件上传接口
 * @param {Object} data - 请求参数
 * @param {File} data.file - 要上传的文件
 * @returns {Promise} 返回文件上传的Promise对象
 */
export function upload(data) {
  return request({
    url: '/common/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    hideloading: true // 添加这一行
  })
}

export function getFileInfo(fileId) {
  return request({
    url: `/info/${fileId}`,
    method: 'get'
  })
}

export async function useDownload(api, tempName, params, fileType = '.xlsx') {
  try {
    console.log(api, tempName, params, fileType)
    const res = await api(params)
    const blob = new Blob([res], { type: getMimeType(fileType) })
    console.log(blob)
    // 2. 兼容旧版 Edge（可选）
    if (navigator.msSaveOrOpenBlob) {
      return navigator.msSaveOrOpenBlob(blob, `${tempName}`)
    }
    // 3. 创建并触发下载
    const blobUrl = URL.createObjectURL(blob)
    console.log(blobUrl)
    const a = document.createElement('a')
    a.href = blobUrl
    a.download = `${tempName}`
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    // 4. 清理资源
    document.body.removeChild(a)
    URL.revokeObjectURL(blobUrl) // 释放内存
  } catch (error) {
    console.log(error)
  }
}

function getMimeType(fileType) {
  const mimeTypeMap = {
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.csv': 'text/csv',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.bmp': 'image/bmp',
    '.txt': 'text/plain',
    '.zip': 'application/zip',
    '.rar': 'application/x-rar-compressed',
    '.mp4': 'video/mp4',
    '.avi': 'video/x-msvideo',
    '.mov': 'video/quicktime'
  }
  return mimeTypeMap[fileType.toLowerCase()] || 'application/octet-stream'
}

/**
 * 记录问题相关信息
 * @param {Object} data - 请求参数
 * @param {string} data.retType - 类型
 *   - "SITE_SELF": 现场自评
 *   - "QUANTITY_SELF": 定量自评
 *   - "SITE_NORMAL": 现场正式
 *   - "QUANTITY_NORMAL": 定量正式
 * @param {integer} data.retId - 返回ID
 * @param {string} data.recordId - 记录ID (定量正式时必填)
 * @returns {Promise} 返回问题记录结果的Promise对象
 */
export function recordQuestion(data) {
  return request({
    url: '/new/review/problem/record/rel/info',
    method: 'post',
    data
  })
}

/**
 *新增记录问题相关信息
 */
export function recordQuestionAdd(data) {
  return request({
    url: '/new/review/problem/record',
    method: 'post',
    data
  })
}
