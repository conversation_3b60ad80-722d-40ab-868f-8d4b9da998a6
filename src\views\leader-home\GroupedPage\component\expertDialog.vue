<template>
  <van-dialog :value="dialogShow" :show-confirm-button="false" :show-cancel-button="false" class="dialog-self"
    title="选择专家" @confirm="onConfirm" @cancel="onCancel" @update:value="updateDialogShow">
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span style="font-weight: bold; color: #333333">选择专家</span>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
      </div>
    </template>
    <div class="content-container">
      <van-radio-group class="user-list" v-model="selectedUserId">
        <div class="user-item" v-for="item in expertData" :key="item.id">
          <van-radio :name="item.id">{{ item.name
          }}</van-radio>
        </div>
      </van-radio-group>
      <div>
        <van-button type="default" class="cancel-btn" @click="onCancel">取消</van-button>
        <van-button type="default" class="confirm-btn" @click="onConfirm">确定</van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { Toast } from 'vant'
export default {
  name: 'ExpertDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    expertData: {
      type: Array,
      default: () => []
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show,
      selectedUserId: null
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    onConfirm() {
      if (!this.selectedUserId) {
        Toast.fail('请选择专家')
        return
      }
      this.$emit('confirm', this.selectedUserId)
      this.selectedUserId = null // Reset selected user after confirmation
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.selectedUserId = null // Reset selected user when dialog is cancelled
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.selectedUserId = null // Reset selected user when dialog is closed
      this.$emit('update:show', value)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}

::v-deep .van-dialog__content {
  padding: 0 22px 22px;
}

.dialog-self {
  width: 800px;
  max-height: 400px;
}

.cancel-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;

  .user-list {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    padding-left: 50px;

  }

  .user-item {
    width: 25%;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-weight: 500;
    font-size: 17px;
    color: #333333;
    flex-shrink: 0;

    ::v-deep .van-radio {
      width: 100%;
      overflow: hidden;
    }

    ::v-deep .van-radio__label {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-left: 5px;
    }
  }

  .cancel-btn {
    width: 172px;
    height: 50px;
    background: #ffffff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #d7d7d7;
    font-weight: 500;
    font-size: 17px;
    color: #333333;
    margin-right: 10px;
  }

  .confirm-btn {
    width: 172px;
    height: 50px;
    background: #01a3f2;
    border-radius: 5px 5px 5px 5px;
    font-weight: 500;
    font-size: 17px;
    color: #ffffff;
  }
}
</style>
