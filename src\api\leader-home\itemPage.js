import request from '@/utils/request'

/**
 * @param {Object} data
 * @param {integer} data.taskId 任务id
 * @param {integer} data.expertId 专家id
 * @returns
 */
export function getQuantProgress(data) {
  return request({
    url: '/new/assess/task/progress/total/progress',
    method: 'post',
    data
  })
}

/**
 * 提交打分-定量
 * @param {Object} data
 * @param {integer} data.id  指标id
 * @param {number} data.checkPercent  分数
 * @param {string} data.checkAdvice 评审意见
 * @returns {Promise}
 */
export function submitQuantResult(data) {
  return request({
    url: '/new/assess/task/progress/save/review',
    method: 'post',
    data
  })
}
