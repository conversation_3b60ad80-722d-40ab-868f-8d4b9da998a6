<template>
  <div class="page_container">
    <!-- 顶栏 20% 高度 -->
    <div class="top-bar">
      <nav-return :title="routerQuery.hosName" />
      <div class="nav-content">
        <div style="display: flex; align-items: center;justify-content: center;">
          <div style="flex: 1;">
            <van-search v-model="searchValue" :clearable="false" shape="round" placeholder="请输入专家名称"
              @search="handleSearch" @cancel="handleReset" />
          </div>
          <div style="display: flex;">
            <van-button style="border: none;color: #01a3f2;" plain type="default" @click="handleSearch">搜索
            </van-button>
            <van-button style="border: none;color: #01a3f2;" plain type="default" @click="handleReset">重置</van-button>
          </div>
        </div>
      </div>
      <UserInfo />
    </div>

    <!-- 主体部分 -->
    <div class="main-container">
      <!-- 右侧主要内容区域 -->
      <div class="main-content">
        <van-empty v-if="projectItems.length === 0" image="search" image-size="300px" description="无数据" />
        <div v-for="(item, index) in projectItems" :key="index" class="menu-item"
          style="display: flex; justify-content: space-between; align-items: center">
          <div class="menu-item-title">
            <div style="display: flex; align-items: center;">
              <div class="menu-item-title-name">{{ item.leaderName }}</div>
            </div>
            <div class="count-text">{{ item.projectCount }}</div>
          </div>
          <div class="menu-item-progress">
            <div style="display: flex; align-items: center">
              <div class="menu-item-category">{{ item.projectCategory }}</div>
              <div style="flex: 1"></div>
            </div>
            <div style="display: flex; align-items: center">
              <div class="progress-title">组内专家进度</div>
              <div style="width: 70%">
                <van-progress :percentage="item.projectProgress" stroke-width="20" :show-pivot="false"
                  :color="item.projectProgress === 100 ? '#72D725' : ''" />
              </div>
              <div class="progress-text">{{ item.projectProgress }}%</div>
            </div>
          </div>
          <div v-if="item.id === userId" class="menu-item-status">
            <div v-if="item.projectStatus === '已复核'" class="status-item-success" @click="navToDetail(item)">
              <img src="@/assets/img/<EMAIL>" class="img-status" alt="success" />
              {{ item.projectStatus }}
            </div>
            <div v-else-if="routerQuery.status !== '未开始'" class="status-item-pending" @click="navToScore(item)">
              组长打分
            </div>
            <div v-else class="status-item-default">
              <img src="@/assets/img/<EMAIL>" class="img-status" alt="default" />
              {{ item.projectStatus }}
            </div>
          </div>
          <div v-else class="menu-item-status">
            <div v-if="item.projectStatus === '已复核'" class="status-item-success" @click="navToDetail(item)">
              <img src="@/assets/img/<EMAIL>" class="img-status" alt="success" />
              {{ item.projectStatus }}
            </div>
            <div v-else-if="item.projectStatus === '指标复核'" class="status-item-pending" @click="navToItem(item)">
              {{ item.projectStatus }}
            </div>
            <div v-else class="status-item-default">
              <img src="@/assets/img/<EMAIL>" class="img-status" alt="default" />

              {{ item.projectStatus }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="submit-container">
      <div style="display: flex; align-items: center;">
        <div>
          <img src="@/assets/img/<EMAIL>" class="img-status" alt="success" />
        </div>
        <div>
          <span class="submit-text"> 请在指标全部复核完毕后，点击右侧按钮提交复核结果 </span>
        </div>
      </div>
      <div>
        <van-button v-if="routerQuery.status === '已完成'" disabled type="default" class="submit-btn">已完成复核
        </van-button>
        <template v-else>
          <van-button v-if="allProjectsComplete" type="default" class="submit-btn-complete" @click="openSubmit()">提交复核结果
          </van-button>
          <van-button v-else disabled type="default" class="submit-btn">提交复核结果</van-button>
        </template>
      </div>
    </div>
    <submit-popup :score="finalScore" :show.sync="submitDialog" @cancel="closeSubmit" @confirm="submit" />
  </div>
</template>

<script>
import {
  getQuantProgress,
  getSiteProgress,
  getSiteCal,
  submitSiteReview,
  getQuantCal,
  submitQuantReview
} from '@/api/leader-home/reviewPage'
import NavReturn from '@/components/navReturn'
import UserInfo from '@/components/userInfo'
import submitPopup from './component/submitPopup.vue'
import { Toast } from 'vant'

export default {
  name: 'HosList',
  components: {
    NavReturn,
    UserInfo,
    submitPopup
  },
  data() {
    return {
      searchValue: '',
      userInfo: this.$store.state.user.userInfo,
      userId: this.$store.state.user.userInfo.user.userId,
      routerQuery: this.$route.query,
      projectItems: [],
      copyProjectItems: [],
      submitDialog: false,
      finalScore: {}
    }
  },
  mounted() {
    if (this.routerQuery.type === '现场') {
      this.getSiteProgress()
    } else {
      this.getQuantProgress()
    }
  },
  computed: {
    allProjectsComplete() {
      return this.projectItems.length > 0 && this.projectItems.every(item => item.projectStatus === '已复核')
    }
  },
  methods: {
    handleSearch() {
      this.projectItems = this.copyProjectItems.filter(item => item.leaderName.includes(this.searchValue))
    },
    handleReset() {
      this.searchValue = ''
      this.projectItems = [...this.copyProjectItems]
    },
    openSubmit() {
      this.fetchFinalScore()
    },
    submit(fileId) {
      console.log('submit', fileId)
      if (this.routerQuery.type === '现场') {
        submitSiteReview({ recordId: this.routerQuery.recordId, expertSign: fileId }).then(res => {
          console.log(res)
          Toast('提交成功')
          this.closeSubmit()
          this.$router.go(-1)
        })
      } else {
        submitQuantReview({ taskId: this.routerQuery.taskId, signId: fileId }).then(res => {
          console.log(res)
          Toast('提交成功')
          this.closeSubmit()
          this.$router.go(-1)
        })
      }
    },
    closeSubmit() {
      this.submitDialog = false
    },
    getQuantProgress() {
      getQuantProgress({ taskId: this.routerQuery.taskId }).then(res => {
        console.log(res)
        this.projectItems.push(
          ...res.data.map(item => {
            return {
              type: '定量',
              id: item.expertId,
              leaderName: item.expertName,
              projectCount: '定量条款监测数量' + item.totalCnt,
              projectCategory: '定量',
              projectProgress: item.totalCnt === 0 ? 0 : (item.optStatus === 'WAIT_REVIEW' || item.optStatus === 'REVIEWING' || item.optStatus === 'REVIEWED') ? 100 : Math.floor((item.cnt / item.totalCnt) * 100),
              projectStatus: this.getQuantStatusText(item.optStatus),
              reviewType: this.routerQuery.type,
              isSign: item.expertSignCnt === item.totalCnt
            }
          })
        )
        this.copyProjectItems = [...this.projectItems]
        console.log(this.projectItems)
      })
    },
    getSiteProgress() {
      getSiteProgress({ recordId: this.routerQuery.recordId }).then(res => {
        console.log(res)
        this.projectItems.push(
          ...res.data.map(item => {
            return {
              type: '现场',
              id: item.expertId,
              leaderName: item.expertName,
              projectCount: '现场条款监测数量' + item.count,
              projectCategory: item.groups.map(group => group.tempGroupName).join(','),
              projectProgress: item.count === 0 ? 0 : Math.floor((item.finishCount / item.count) * 100),
              projectStatus: this.getSiteStatusText(item.reviewStatus),
              reviewType: this.routerQuery.type,
              isSign: true
            }
          })
        )
        console.log(this.projectItems)
        this.copyProjectItems = [...this.projectItems]
        if (this.routerQuery.configPlan === 'true' && this.routerQuery.reviewType === 'INSPECT') {
          console.log('configPlan', '现场关联了定量')
          this.getQuantProgress()
        }
      })
    },
    fetchFinalScore() {
      if (this.routerQuery.type === '现场') {
        getSiteCal({ recordId: this.routerQuery.recordId }).then(res => {
          console.log(res)
          this.finalScore = {
            type: this.routerQuery.type,
            method: this.routerQuery.method,
            quantScore: res.data.famxId ? res.data.quatScore : null,
            siteScore: this.routerQuery.method === '选项' ? null : res.data.totalScore,
            optionScore: this.routerQuery.method === '选项' ? res.data.optionsScore : null,
            optionScoreCore: this.routerQuery.method === '选项' ? res.data.optionsCore : null
          }
          this.submitDialog = true
          console.log(this.finalScore)
        })
      } else {
        getQuantCal(this.routerQuery.taskId).then(res => {
          console.log(res)
          this.finalScore = {
            type: this.routerQuery.type,
            method: this.routerQuery.method,
            quantScore: res.data.totalScore
          }
          this.submitDialog = true
          console.log(this.finalScore)
        })
      }
    },
    getSiteStatusText(status) {
      switch (status) {
        case 'FINISHED':
          return '已复核'
        case 'WAIT_CHECK':
          return '指标复核'
        case 'DOING':
          return '未复核'
        case 'NONE':
          return '未复核'
        case 'NO_START':
          return '未复核'
        default:
          return '未知状态'
      }
    },
    getQuantStatusText(status) {
      switch (status) {
        case 'REVIEWED':
          return '已复核'
        case 'WAIT_REVIEW':
          return '指标复核'
        case 'REVIEWING':
          return '指标复核'
        case 'UNASSIGNED':
          return '未复核'
        case 'UNBEIGNED':
          return '未复核'
        case 'UNREVIEWED':
          return '未复核'
        default:
          return '未知状态'
      }
    },
    // 在专家复核界面，定量和现场彻底分开，不存在关联。
    navToDetail(item) {
      this.$router.push({
        path: '/leader-detail',
        query: {
          ...this.routerQuery,
          configPlan: 'false',
          type: item.type,
          expertId: item.id,
          expertName: item.leaderName
        }
      })
    },
    navToItem(item) {
      if (item.isSign) {
        this.$router.push({
          path: '/leader-item',
          query: {
            ...this.routerQuery,
            configPlan: 'false',
            type: item.type,
            expertId: item.id,
            expertName: item.leaderName
          }
        })
      } else {
        Toast({
          message: '请评审专家签字后再进行复核'
        })
      }
    },
    navToScore(item) {
      this.$router.push({
        path: '/leader-score',
        query: {
          ...this.routerQuery,
          configPlan: 'false',
          type: item.type,
          expertId: item.id,
          expertName: item.leaderName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.img-status {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.page_container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f2f5f8;

  .top-bar {
    height: 60px;
    flex-shrink: 0;
    background: #ffffff;
    display: flex;
    align-items: center;

    .nav-content {
      flex: 1;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .main-content {
      flex: 1;
      padding: 10px;
      padding-bottom: 120px;
      /* 为底部固定的 submit-container 留出空间 */
      overflow-y: auto;
      display: flex;
      flex-direction: column;

      .menu-item {
        box-sizing: border-box;
        width: 100%;
        height: 78px;
        /* 确保高度生效 */
        min-height: 78px;
        /* 增加最小高度，防止内容为空时高度塌陷 */
        margin: 5px 0;
        padding: 0 10px;
        background: #ffffff;
        border-radius: 5px 5px 5px 5px;
        gap: 10px;

        .menu-item-title {
          display: flex;
          flex-direction: column;

          .menu-item-title-name {
            width: 250px;
            height: 32px;
            line-height: 32px;
            font-weight: bold;
            font-size: 18px;
            color: #124e7e;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .count-text {
            height: 14px;
            font-weight: 500;
            font-size: 14px;
            color: #999999;
          }
        }

        .menu-item-progress {
          flex: 1;

          .menu-item-category {
            padding: 5px 10px;
            background: #e0f3ff;
            border-radius: 10px 10px 10px 10px;
            font-weight: 500;
            font-size: 14px;
            color: #0075e2;
            text-align: center;
          }

          .progress-title {
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: #333333;
            margin-right: 10px;
          }

          .progress-text {
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 16px;
            margin-left: 10px;
          }
        }

        .menu-item-status {
          width: 250px;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .status-item-success {
            width: 101px;
            height: 30px;
            background: #e7ffd4;
            border-radius: 30px 30px 30px 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 16px;
            color: #999999;
          }

          .status-item-default {
            width: 101px;
            height: 30px;
            background: #f5f5f5;
            border-radius: 30px 30px 30px 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: bold;
            font-size: 16px;
            color: #999999;
          }

          .status-item-pending {
            width: 101px;
            height: 30px;
            background: #01a3f2;
            border-radius: 30px 30px 30px 30px;
            border: 1px solid #01a3f2;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }
        }
      }
    }
  }

  .submit-container {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    min-height: 70px;
    height: 70px;
    background: #ffffff;
    border-radius: 20px 20px 0px 0px;
    box-shadow: 0px -2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;

    .submit-text {
      height: 32px;
      font-weight: 500;
      font-size: 16px;
      line-height: 32px;
      color: #000000;
    }

    .submit-btn {
      width: 167px;
      height: 40px;
      background: #f5f5f5;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #d7d7d7;
      font-weight: bold;
      font-size: 16px;
      color: #d7d7d7;
    }

    .submit-btn-complete {
      width: 167px;
      height: 40px;
      background: #01a3f2;
      border-radius: 5px 5px 5px 5px;
      border: 1px solid #d7d7d7;
      font-weight: bold;
      font-size: 16px;
      color: #ffffff;
    }
  }
}
</style>
