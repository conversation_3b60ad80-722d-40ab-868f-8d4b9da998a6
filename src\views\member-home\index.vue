<template>
  <div class="page_container">
    <!-- 顶栏 20% 高度 -->
    <div class="top-bar">
      <HeadLogo />
      <div class="nav-content">
        <div></div>
      </div>
      <UserInfo />
    </div>

    <!-- 主体部分 -->
    <div class="main-container">
      <!-- 左侧侧边栏 20% 宽度 -->
      <div class="sidebar">
        <div
          v-for="(item, index) in years"
          :key="index"
          class="menu-item"
          :class="{ active: selectedYear === item }"
          @click="fetchDetails(item)"
        >
          {{ item }}年度
        </div>
      </div>

      <!-- 右侧主要内容区域 -->
      <div class="main-content">
        <div
          v-for="(item, index) in details"
          :key="index"
          class="menu-item"
          style="display: flex; flex-direction: column; justify-content: center"
        >
          <div style="display: flex; justify-content: space-between; align-items: center">
            <div style="display: flex; align-items: center">
              <div style="margin-right: 20px">
                <img src="@/assets/img/member-home/<EMAIL>" width="62px" height="62px" alt="hospital" />
              </div>
              <div>
                <div style="display: flex; align-items: center">
                  <span class="review-hospital-name">
                    {{ item.checkHosName }}
                  </span>
                  <span class="review-hospital-level">
                    {{
                      item.hospitalLevelCode === '20' ? '二级医院' : item.hospitalLevelCode === '30' ? '三级医院' : '--'
                    }}
                  </span>
                </div>
                <div class="review-group">评审组：{{ item.groupName }} 组长：{{ item.leaderName }}</div>
              </div>
            </div>
            <div @click="navToReview(item)" style="display: flex; align-items: center">
              <div v-if="item.reviewStatus === '已完成'" class="review-status-completed">
                {{ item.reviewStatus }}
              </div>
              <div v-else-if="item.reviewStatus === '复核中'" class="review-status-reviewing">
                {{ item.reviewStatus }}
              </div>
              <div v-else-if="item.reviewStatus === '评审中'" class="review-status-reviewing">
                {{ item.reviewStatus }}
              </div>
              <div v-else-if="item.reviewStatus === '未开始'" class="review-status-unstarted">
                {{ item.reviewStatus }}
              </div>
              <div style="margin-left: 45px">
                <img src="@/assets/img/member-home/<EMAIL>" width="20px" height="20px" alt="detail" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getReviewListByYear, getExpertReviewYears } from '@/api/member-home/index'
import HeadLogo from '@/components/headLogo'
import UserInfo from '@/components/userInfo'
export default {
  name: 'MemberHome',
  components: {
    HeadLogo,
    UserInfo
  },
  data() {
    return {
      userInfo: this.$store.state.user.userInfo,
      years: [],
      details: [],
      selectedYear: null // 新增：存储当前选中的年份
    }
  },
  created() {
    console.log(this.userInfo)
    this.fetchYears()
  },
  computed: {},
  methods: {
    fetchYears() {
      getExpertReviewYears({}).then(res => {
        console.log(res)
        this.years = res.rows.map(item => item.reviewYear)
        if (this.years.length > 0) {
          this.selectedYear = this.years[0] // 默认选中第一个年份
          this.fetchDetails(this.selectedYear)
        } else {
          this.years.push(String(new Date().getFullYear()))
          this.selectedYear = this.years[0] // 默认选中第一个年份
          this.fetchDetails(this.selectedYear)
        }
      })
    },
    fetchDetails(item) {
      this.selectedYear = item // 点击年份时，更新 selectedYear
      getReviewListByYear({ reviewYear: item }).then(res => {
        console.log(res)
        this.details = res.rows
      })
    },
    navToReview(item) {
      let path = ''
      if (item.reviewStatus === '未开始') {
        path = '/member-not-started'
      } else if (item.reviewStatus === '评审中') {
        path = '/member-in-review'
      } else if (item.reviewStatus === '复核中') {
        path = '/member-in-re-review'
      } else if (item.reviewStatus === '已完成') {
        path = '/member-completed'
      }
      this.$router.push({
        path: path,
        query: {
          recordId: item.recordId,
          taskId: item.taskId,
          hosName: item.checkHosName,
          planType: item.planType,
          status: item.planType === 'QUANTITATIVE_METRICS' ? item.evalStatus : item.reviewStatus,
          method: item.reviewMode
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page_container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f2f5f8;
  .top-bar {
    height: 60px;
    flex-shrink: 0;
    background: #ffffff;
    display: flex;
    align-items: center;
    .nav-content {
      flex: 1;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .sidebar {
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      /* justify-content: center;  删除垂直居中, 让列表从顶部开始显示 */
      width: 140px;
      height: calc(100vh - 60px);
      overflow-y: auto;
      padding-top: 10px; /* 增加顶部内边距，使列表不紧贴顶部 */
      .menu-item {
        display: flex;
        align-items: center;
        justify-content: center; /* 添加水平居中 */
        width: 124px;
        min-height: 40px;
        border-radius: 20px 20px 20px 20px;
        font-weight: bold;
        font-size: 16px;
        color: #666666;
        margin: 0;
        cursor: pointer;
        &.active {
          background: #124e7e;
          color: #ffffff;
        }
      }
    }

    .main-content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      .menu-item {
        box-sizing: border-box;
        width: 100%;
        height: 109px; /* 确保高度生效 */
        min-height: 109px; /* 增加最小高度，防止内容为空时高度塌陷 */
        background: #ffffff;
        border-radius: 20px 20px 20px 20px;
        margin: 5px 0;
        padding: 20px;
        .review-hospital-name {
          height: 28px;
          font-weight: bold;
          font-size: 16px;
          color: #000000;
          display: flex;
          align-items: center;
          margin-right: 10px;
        }
        .review-hospital-level {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          height: 24px;
          padding: 0px 5px;
          background: #124e7e;
          border-radius: 5px 5px 5px 5px;

          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
        }
        .review-group {
          height: 22px;
          font-weight: 500;
          font-size: 14px;
          color: #7d94a7;
          margin-top: 5px;
        }
        .review-status-unstarted {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 32px;
          background: #ececec;
          border-radius: 10px 10px 10px 10px;
          font-weight: 500;
          font-size: 16px;
          color: #666666;
          line-height: 19px;
        }
        .review-status-reviewing {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 32px;
          background: #e0f3ff;
          border-radius: 10px 10px 10px 10px;
          font-weight: 500;
          font-size: 16px;
          color: #0075e2;
        }
        .review-status-completed {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 32px;
          background: #e2ffe0;
          border-radius: 10px 10px 10px 10px;
          font-weight: 500;
          font-size: 16px;
          color: #15b100;
        }
        .review-look-btn {
          width: 115px;
          height: 40px;
          background: #ffffff;
          border-radius: 30px 30px 30px 30px;
          border: 1px solid #01a3f2;
          font-weight: 500;
          font-size: 16px;
          color: #01a3f2;
          margin-right: 10px;
        }
        .review-start-btn {
          width: 115px;
          height: 40px;
          background: #01a3f2;
          border-radius: 30px 30px 30px 30px;
          border: 1px solid #01a3f2;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
