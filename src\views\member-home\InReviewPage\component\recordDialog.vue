<template>
  <van-dialog :value="dialogShow" :show-confirm-button="false" :show-cancel-button="false" class="dialog-self"
    title="专家信息" @confirm="onConfirm" @cancel="onCancel" @update:value="updateDialogShow">
    <template #title>
      <div class="dialog-title">
        <span class="title-text">问题记录</span>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
      </div>
    </template>
    <div class="content-container">
      <div class="input-section">
        <van-field v-model="problemDescription" type="textarea" placeholder="请详细描述发现的问题..." class="problem-input"
          :border="false" :maxlength="inputConfig.maxlength" :rows="inputConfig.rows"
          :show-word-limit="inputConfig.showWordLimit" autosize />
      </div>
      <div>
        <van-button type="default" class="cancel-btn" @click="onCancel">取消</van-button>
        <van-button type="default" class="confirm-btn" @click="onConfirm">确定</van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'RecordDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    recordDialogData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show,
      problemDescription: ''
    }
  },
  computed: {
    inputConfig() {
      return {
        maxlength: 500,
        rows: 2,
        showWordLimit: true
      }
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
      if (newValue) {
        this.problemDescription = this.recordDialogData.desp || ''
      }
    }
  },
  methods: {
    onConfirm() {
      this.$emit('confirm', this.problemDescription)
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}

::v-deep .van-dialog__content {
  padding: 0px 22px 22px;
}

.dialog-self {
  width: 600px;
}

.cancel-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.dialog-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-text {
  font-weight: bold;
  font-size: 18px;
  color: #333333;
}

.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;

  .input-section {
    width: 100%;

    .input-label {
      font-size: 15px;
      color: #333333;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .problem-input {
      width: 100%;
      background: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e1e5e9;
      transition: all 0.2s ease;

      &:active,
      &:focus-within {
        border-color: #01a3f2;
        background: #ffffff;
      }

      ::v-deep .van-field__control {
        min-height: 80px;
        resize: none;
        background: transparent;
        border: none;
        outline: none;
        font-size: 16px;
        line-height: 1.5;
        color: #333333;
        padding: 12px;
        border-radius: 6px;

        &::placeholder {
          color: #999999;
          font-size: 15px;
        }
      }

      ::v-deep .van-field__word-limit {
        color: #999999;
        font-size: 12px;
        padding: 0 12px 8px;
        text-align: right;
        background: transparent;
      }

      &.van-field--focused {
        border-color: #01a3f2;
        background: #ffffff;
      }
    }
  }

  .cancel-btn {
    width: 172px;
    height: 50px;
    background: #ffffff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #d7d7d7;
    font-weight: 500;
    font-size: 17px;
    color: #333333;
    margin-right: 10px;
  }

  .confirm-btn {
    width: 172px;
    height: 50px;
    background: #01a3f2;
    border-radius: 5px 5px 5px 5px;
    font-weight: 500;
    font-size: 17px;
    color: #ffffff;
  }
}
</style>
