/**
 * 身份证号码验证
 * @param {string} code - 身份证号码
 * @returns {boolean} - 返回验证结果，true为合法，false为不合法
 */
export function identityCodeValid(code) {
  // 非空校验
  if (!code) {
    return false
  }

  // 长度校验
  if (code.length !== 18 && code.length !== 15) {
    return false
  }

  // 15位身份证转为18位
  if (code.length === 15) {
    code = convertTo18(code)
  }

  // 检查格式
  const pattern = /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/
  if (!pattern.test(code)) {
    return false
  }

  // 检查日期是否有效
  const year = parseInt(code.substr(6, 4))
  const month = parseInt(code.substr(10, 2))
  const day = parseInt(code.substr(12, 2))

  if (!isValidDate(year, month, day)) {
    return false
  }

  // 校验码验证
  return checkCode(code)
}

/**
 * 15位身份证转18位
 * @param {string} code - 15位身份证号码
 * @returns {string} - 返回18位身份证号码
 */
function convertTo18(code) {
  // 15位身份证号：6位地区码 + 6位出生年月日(YYMMDD) + 3位顺序码
  // 18位身份证号：6位地区码 + 8位出生年月日(YYYYMMDD) + 3位顺序码 + 1位校验码

  // 将15位身份证升级为18位
  const newCode = code.slice(0, 6) + '19' + code.slice(6)

  // 计算校验码
  const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += parseInt(newCode[i]) * factor[i]
  }

  return newCode + parity[sum % 11]
}

/**
 * 检查日期是否有效
 * @param {number} year - 年
 * @param {number} month - 月
 * @param {number} day - 日
 * @returns {boolean} - 返回日期是否有效
 */
function isValidDate(year, month, day) {
  // 检查月份
  if (month < 1 || month > 12) {
    return false
  }

  // 检查日期
  const maxDays = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]

  // 处理闰年2月
  if (month === 2 && ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0)) {
    maxDays[2] = 29
  }

  return day > 0 && day <= maxDays[month]
}

/**
 * 校验18位身份证的校验码
 * @param {string} code - 18位身份证号码
 * @returns {boolean} - 返回校验结果
 */
function checkCode(code) {
  // 加权因子
  const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]

  // 校验码对应值
  const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += parseInt(code[i]) * factor[i]
  }

  const remainder = sum % 11
  const checkCodeChar = parity[remainder]

  // 校验位可能是X或x
  return checkCodeChar.toUpperCase() === code[17].toUpperCase()
}
