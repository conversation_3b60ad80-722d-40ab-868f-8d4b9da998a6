<template>
  <van-dialog :value="dialogShow" :show-confirm-button="false" :show-cancel-button="false" class="dialog-self" title="医院信息"
    @confirm="onConfirm" @cancel="onCancel" @update:value="updateDialogShow">
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span style="font-weight: bold; color: #333333">医院信息</span>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
      </div>
    </template>
    <div class="content-container">
      <div class="item-container">
        <div class="item-title">医院名称</div>
        <div class="subitem-container">
          <div class="subitem-name">{{ infoData.name || '--' }}</div>
        </div>
      </div>
      <div class="item-container">
        <div class="item-title">医院等级</div>
        <div class="subitem-container">
          <div class="subitem-name">{{ infoData.level || '--' }}</div>
        </div>
      </div>
      <div class="item-container">
        <div class="item-title">评审类型</div>
        <div class="subitem-container">
          <div class="subitem-name">{{ infoData.type || '--' }}</div>
        </div>
      </div>
      <div class="item-container">
        <div class="item-title">评审组别</div>
        <div class="subitem-container">
          <div class="subitem-name">{{ infoData.group || '--' }}</div>
        </div>
      </div>
      <div class="item-container">
        <div class="item-title">评审组长</div>
        <div class="subitem-container">
          <div class="subitem-name">{{ infoData.leader || '--' }}</div>
        </div>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'InfoDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    infoData: {
      type: Object,
      default: null
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    onConfirm() {
      this.$emit('confirm')
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}

::v-deep .van-dialog__content {
  padding: 0 22px 22px;
}

.dialog-self {
  width: 600px;
}

.cancel-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.content-container {
  padding: 20px 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.item-container {
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eaeaea;

  &:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }
}

.item-title {
  width: 120px;
  font-size: 15px;
  font-weight: 500;
  color: #666;
  padding-right: 15px;
}

.subitem-container {
  flex: 1;
}

.subitem-name {
  font-size: 15px;
  color: #333;
  font-weight: 400;
  line-height: 1.5;
  word-break: break-all;
  padding: 0 5px;
}
</style>
