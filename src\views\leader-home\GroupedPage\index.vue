<template>
  <div class="page_container">
    <!-- 顶栏 20% 高度 -->
    <div class="top-bar">
      <nav-return :title="routerQuery.hosName" />
      <div class="nav-content">
        <van-tabs type="card" swipeable @click="handleNavClick" color="#124e7e">
          <van-tab :title-style="{ border: 'none', borderRadius: '10px' }" v-for="(item, index) in navList" :key="index"
            :title="item.name" :name="item.id" />
        </van-tabs>
      </div>
      <UserInfo />
    </div>

    <!-- 主体部分 -->
    <div class="main-container">

      <!-- 右侧主要内容区域 -->
      <div class="main-content">
        <div class="item-container">
          <div class="top-part">
            <div class="title">本组指标 {{ categoryList.total }} &nbsp;&nbsp;已选指标 {{ selectedResult.length }}</div>
            <div style="flex:1;">
              <!-- <van-search v-model="searchValue" :clearable="false" shape="round" placeholder="请输入指标名称"
                @search="handleSearch" @cancel="handleReset" /> -->
            </div>
            <div> <van-button type="default" class="grouped-btn"
                @click="cancleCheck(categoryList.list.length)">取消选择</van-button>
            </div>
            <div> <van-button type="default" class="grouped-btn-border" @click="handleGrouped">调整专家</van-button>
            </div>
          </div>
          <div class="list-part">
            <van-checkbox-group v-model="selectedResult">
              <van-cell-group>
                <van-cell class="list-item" v-for="(item, index) in categoryList.list" :clickable="!item.status"
                  :key="index" @click="toggleCheck(index)">
                  <template #icon>
                    <van-checkbox shape="square" :name="item.id" ref="checkboxes" :disabled="item.status" />
                  </template>
                  <div style="display: flex;justify-content: space-between;">
                    <div class="left-text">
                      <span>{{ item.level }} {{ item.label }}</span>
                    </div>
                    <div class="right-text">
                      <span v-if="item.status" class="done-text">
                        已完成
                      </span>
                      <span v-else class="none-text">
                        未开始
                      </span>
                      <span>
                        {{ item.expertName }}
                      </span>
                    </div>
                  </div>
                </van-cell>
              </van-cell-group>
            </van-checkbox-group>
          </div>
          <!-- <div class="bottom-part">
            <van-pagination @change="handlePageChange" v-model="pageNum" :page-count="totalPage" mode="simple">
              <template #prev-text>
                <van-icon color="#124e7e" name="arrow-left" />
              </template>
              <template #next-text>
                <van-icon color="#124e7e" name="arrow" />
              </template>
              <template #page="{ text }">{{ text }}</template>
            </van-pagination>
          </div> -->
        </div>
      </div>
    </div>
    <expert-dialog :show.sync="expertDialog.show" :expert-data="expertDialog.expertData" @cancel="closeDialog"
      @confirm="confirmExpertDialog" />

  </div>
</template>

<script>
import { Toast } from 'vant'
import {
  getSiteGroup,
  getSiteResultListV2,
  getQuantResultList,
  listExperts,
  listExpertsInside,
  siteAssignExpert,
  quantAssignExpert
} from '@/api/member-home/common'
import UserInfo from '@/components/userInfo'
import NavReturn from '@/components/navReturn'
import expertDialog from './component/expertDialog'

export default {
  name: 'InReviewPage',
  components: {
    UserInfo,
    NavReturn,
    expertDialog
  },
  data() {
    return {
      searchValue: '',
      userInfo: this.$store.state.user.userInfo,
      routerQuery: this.$route.query,
      doneDialog: false,
      openType: null,
      scoreDialog: false,
      quantDialog: false,
      optionDialog: false,
      customDialog: false,
      navList: [],
      selectNav: null,
      categoryList: [],
      selectCategoryId: null,
      pageNum: 1,
      pageSize: 100,
      totalPage: 1,
      fileUrls: [],
      fileBlobs: [],
      selectedResult: [],
      expertDialog: {
        show: false,
        expertData: []
      }
    }
  },
  created() {
    console.log(this.userInfo)
    this.fetchNavList()
    if (this.routerQuery.reviewType === 'SELF') {
      listExpertsInside(this.routerQuery.groupId).then(
        res => {
          console.log('专家列表:', res)
          this.expertDialog.expertData = res.data.expList.map(item => {
            return { id: item.userId, name: item.nickName }
          })
        }
      )
    } else {
      listExperts(this.routerQuery.groupId).then(
        res => {
          console.log('专家列表:', res)
          this.expertDialog.expertData = res.data.expList.map(item => {
            return { id: item.userId, name: item.nickName }
          })
        }
      )
    }
  },
  computed: {
    currentIndex() {
      return this.categoryList.list?.findIndex(item => item.id === this.selectCategoryId)
    }
  },
  methods: {
    toggleCheck(index) {
      // 检查当前项的状态，如果已完成则不触发切换
      const currentItem = this.categoryList.list[index]
      if (currentItem && currentItem.status === true) {
        Toast.fail('该指标已完成，无法调整')
        return // 已完成的项目不允许切换
      }
      this.$refs.checkboxes[index].toggle()
    },
    cancleCheck(len) {
      for (let i = 0; i < len; i++) {
        this.$refs.checkboxes[i].toggle(false)
      }
    },
    handleGrouped() {
      console.log(this.selectedResult)
      if (this.selectedResult.length === 0) {
        Toast.fail('请先选择指标')
        return
      } else {
        this.expertDialog.show = true
      }
    },
    confirmExpertDialog(userId) {
      console.log('选择的专家ID:', userId)
      console.log('选择的指标ID:', this.selectedResult)
      if (this.selectNav.title === '定量指标') {
        quantAssignExpert({
          taskId: this.routerQuery.taskId,
          userId: userId,
          list: this.selectedResult.map(item => {
            return { id: item }
          })
        }).then(res => {
          Toast.success('分配成功')
          this.selectedResult = [] // 清空已选择的指标
          this.fetchQuantResultList(true)
        })
      } else {
        siteAssignExpert({
          assignId: this.categoryList.assignId,
          expertId: userId,
          standardItemIds: this.selectedResult
        }).then(res => {
          Toast.success('分配成功')
          this.selectedResult = [] // 清空已选择的指标
          this.fetchSiteResultList(true)
        })
      }
      this.expertDialog.show = false
    },
    closeDialog() {
      this.expertDialog.show = false
    },
    handleSearch() {
      console.log('searchValue', this.searchValue)
      if (this.selectNav.title === '定量指标') {
        this.fetchQuantResultList()
      } else {
        this.fetchSiteResultList()
      }
    },
    handleReset() {
      this.searchValue = ''
      if (this.selectNav.title === '定量指标') {
        this.fetchQuantResultList()
      } else {
        this.fetchSiteResultList()
      }
    },
    fetchNavList() {
      if (this.routerQuery.type === '现场') {
        getSiteGroup({
          standardId: this.routerQuery.standardId,
          templateId: this.routerQuery.templateId,
          checkGroupType: 'GROUPED'
        }).then(res => {
          console.log(res)
          this.navList = res.data.map(item => {
            return { id: item.id, name: item.groupName }
          })
          console.log('是否关联定量:', this.routerQuery.configPlan)
          if (this.routerQuery.configPlan === 'true' && this.routerQuery.reviewType === 'INSPECT') {
            this.navList.push({ id: 12345678, name: '定量指标' })
          }
          this.handleNavClick(this.navList[0].id, this.navList[0].name)
        })
      } else {
        this.navList = [{ id: 12345678, name: '定量指标' }]
        this.handleNavClick(this.navList[0].id, this.navList[0].name)
      }
    },
    handleNavClick(name, title) {
      this.searchValue = ''
      this.selectedResult = []
      console.log(name, title)
      this.selectNav = {
        id: name,
        title: title
      }
      if (this.selectNav.title === '定量指标') {
        this.fetchQuantResultList()
      } else {
        this.fetchSiteResultList()
      }
    },
    handlePageChange(page) {
      console.log(page)
      this.pageNum = page
      this.handleNavClick(this.selectNav.id, this.selectNav.title)
    },
    fetchSiteResultList(hidden = false) {
      getSiteResultListV2(
        {
          groupId: this.selectNav.id,
          recordId: this.routerQuery.recordId,
          templateId: this.routerQuery.templateId,
          reviewMode: this.routerQuery.reviewMode,
          reviewType: this.routerQuery.reviewType
        }, {}, hidden
      ).then(res => {
        if (res.data.expertList.length === 0) {
          Toast.fail('无数据')
          this.categoryList = {
            total: 0,
            selected: 0,
            list: []
          }
          return
        }
        console.log(res)
        // this.totalPage = Math.ceil(res.total / this.pageSize)
        this.categoryList = {
          total: res.data.expertList.length,
          selected: 0,
          assignId: res.data.id,
          list: res.data.expertList.map(item => {
            return {
              id: item.standardItemId,
              level: item.standardItemChapter,
              label: item.standardItemName,
              status: item.reviewFinish,
              expertName: item.expertName
            }
          })
        }
        console.log(this.categoryList)
      })
    },
    fetchQuantResultList(hidden = false) {
      getQuantResultList(
        {
          taskId: this.routerQuery.taskId,
          zbMc: this.searchValue
        }, {}, hidden
      ).then(res => {
        if (res.rows.length === 0) {
          Toast.fail('无数据')
          this.categoryList = {
            total: 0,
            selected: 0,
            list: []
          }
          return
        }
        // this.totalPage = Math.ceil(res.total / this.pageSize)
        this.categoryList = {
          total: res.rows.length,
          selected: 0,
          list: res.rows.map(item => {
            return {
              id: item.item.id,
              level: item.zbBm,
              label: item.zbMc,
              status: item.item.evalStatus === 'REVIEWED' || item.item.evalStatus === 'SCORED',
              expertName: item.item.expertName
            }
          })
        }
        console.log(this.categoryList)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-tabs__nav--card {
  border: none;
}

.page_container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f2f5f8;

  .top-bar {
    height: 60px;
    flex-shrink: 0;
    background: #ffffff;
    display: flex;
    align-items: center;

    .nav-content {
      flex: 1;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .main-content {
      flex: 1;
      padding: 10px;
      // overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 0px;
    }
  }
}

.item-container {
  flex: 1;
  width: 100%;
  min-height: 300px;
  padding: 10px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .top-part {
    display: flex;
    align-items: center;

    .title {
      width: 240px;
      height: 38px;
      background: #f5f5f5;
      border-radius: 20px 20px 20px 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 16px;
      color: #666666;
    }

    .grouped-btn {
      width: 115px;
      height: 38px;
      background: #ffffff;
      border-radius: 30px 30px 30px 30px;
      border: none;
      font-weight: 500;
      font-size: 16px;
      color: #01a3f2;
    }

    .grouped-btn-border {
      width: 115px;
      height: 38px;
      background: #ffffff;
      border-radius: 30px 30px 30px 30px;
      border: 1px solid #01a3f2;
      font-weight: 500;
      font-size: 16px;
      color: #01a3f2;
    }

  }

  .list-part {
    flex: 1;
    overflow-y: auto;

    .list-item {
      .left-text {
        margin-left: 10px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #191D23;
        text-align: left;
        font-style: normal;
        text-transform: none;
        max-width: 800px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .right-text {
        margin-right: 10px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        text-align: right;
        font-style: normal;
        text-transform: none;

        .done-text {
          color: #72D725;
          margin-right: 10px;
        }

        .none-text {
          color: #999999;
          margin-right: 10px;
        }
      }

    }

  }

  .bottom-part {
    height: 38px;
    border-radius: 5px 5px 5px 5px;
    font-weight: bold;
    font-size: 16px;
    color: #d7d7d7;
  }
}
</style>
