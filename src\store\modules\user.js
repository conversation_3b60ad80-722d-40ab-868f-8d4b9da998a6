import { login, logout, getInfo, getExpInfo } from '@/api/user'
import { getToken, setToken, removeToken, getRefreshToken, removeRefreshToken } from '@/utils/auth'
import { getOrgan } from '@/utils/organ'
// import { getOrgan, setOrgan, removeOrgan } from '@/utils/organ'
const user = {
  state: {
    token: getToken(),
    refreshToken: getRefreshToken(),
    name: '',
    nickName: '',
    avatar: '',
    introduction: '',
    roles: [],
    permissions: [],
    id: '',
    deptId: '',
    expId: '',
    expName: '',
    userInfo: {},
    organ: getOrgan(),
    isNeedAuth: false,
    isAuth: false
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_REFRESH_TOKEN: (state, token) => {
      state.refreshToken = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_NICKNAME: (state, nickName) => {
      state.nickName = nickName
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_ID(state, data) {
      state.id = data
    },
    SET_DEPTID(state, data) {
      state.deptId = data
    },
    SET_EXPID(state, data) {
      state.expId = data
    },
    SET_ORGAN: (state, organ) => {
      state.organ = organ
    },
    SET_EXPNAME(state, data) {
      state.expName = data
    },
    SET_USERINFO(state, data) {
      state.userInfo = data
    },
    SET_ISNEEDAUTH(state, data) {
      state.isNeedAuth = data
    },
    SET_ISAUTH(state, data) {
      state.isAuth = data
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      // 构建登录数据对象
      const loginData = {}

      // 账号密码登录
      if (userInfo.username && userInfo.password) {
        loginData.username = userInfo.username.trim()
        loginData.password = userInfo.password
        if (userInfo.code) loginData.code = userInfo.code
        if (userInfo.uuid) loginData.uuid = userInfo.uuid
      }

      // 手机验证码登录
      if (userInfo.userPhone && userInfo.validCode) {
        loginData.userPhone = userInfo.userPhone
        loginData.validCode = userInfo.validCode
      }

      return new Promise((resolve, reject) => {
        login(loginData)
          .then(res => {
            console.log(res)
            // localStorage.setItem('id', res.data.sysUser.userId)
            // commit('SET_ID', res.data.sysUser.userId)
            // commit('SET_DEPTID', res.data.sysUser.deptId)
            // setToken(res.access_token)
            setToken(res.token)
            // setRefreshToken(res.refresh_token) // 刷新token
            // commit('SET_TOKEN', res.access_token)
            commit('SET_TOKEN', res.token)
            // commit('SET_ORGAN', JSON.stringify(res.data.sysUser.loginOrgan))
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 获取专家信息
    GetExpInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        // const param = {
        //   userId: localStorage.getItem('id').toString()
        // }
        getExpInfo()
          .then(res => {
            res = res.data[0]
            console.log(res.expertName)
            commit('SET_EXPID', res.id)
            commit('SET_EXPNAME', res.expertName)
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 获取用户信息,获取所在医院code
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token)
          .then(res => {
            const user = res.user
            console.log(user)
            commit('SET_EXPID', user.userId)
            commit('SET_EXPNAME', user.nickName)
            commit('SET_USERINFO', res)
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.roles)
              commit('SET_PERMISSIONS', res.permissions)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
            }
            commit('SET_NAME', user.userName)
            commit('SET_NICKNAME', user.nickName)
            commit('SET_ISNEEDAUTH', user.needAuth)
            commit('SET_ISAUTH', user.auth)
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '')
            commit('SET_ROLES', [])
            commit('SET_PERMISSIONS', [])
            removeToken()
            removeRefreshToken()
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        removeRefreshToken()
        resolve()
      })
    }
    // 刷新token
    // RefreshToken({ commit }) {
    //   return new Promise((resolve, reject) => {
    //     refreshToken(getRefreshToken())
    //       .then(res => {
    //         localStorage.setItem('id', res.user_id)
    //         setToken(res.access_token)
    //         setRefreshToken(res.refresh_token) // 刷新token
    //
    //         commit('SET_TOKEN', res.access_token)
    //         resolve()
    //       })
    //       .catch(error => {
    //         reject(error)
    //       })
    //   })
    // }
  }
}
export default user
