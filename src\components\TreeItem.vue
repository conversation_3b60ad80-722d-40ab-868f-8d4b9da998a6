<template>
  <div class="tree-item">
    <div class="rules-content">
      <span>评分细则：</span><span>{{ item.reviewRules ? item.reviewRules : '--' }}</span>
    </div>
    <div class="item-container" v-for="subItem in item.retItemList" :key="subItem.id">
      <!-- Different types of content display -->
      <div v-if="subItem.item.itemType === 'RADIO'">
        <div style="display: flex; flex-direction: column">
          <div class="radio-title-container">
            <span class="radio-title-text"> {{ subItem.item.optTitle ? subItem.item.optTitle : '--' }} </span>
          </div>
          <div class="radio-options-container">
            <van-radio-group :disabled="disabled" direction="horizontal" v-model="subItem.optSelected"
              @change="onCustomItemChange(subItem, 'RADIO')">
              <van-radio v-for="(option, optionIndex) in subItem.item.optList" :key="optionIndex" :name="optionIndex">
                {{ option.optName }}
              </van-radio>
            </van-radio-group>
          </div>
        </div>
      </div>
      <div v-if="subItem.item.itemType === 'INPUT'" class="input-container">
        <div class="input-prefix-container" v-if="subItem.item.prefix">
          <span class="input-prefix-text">{{ subItem.item.prefix }}</span>
        </div>
        <div class="input-field-container">
          <van-field :disabled="disabled" class="uni-input" v-model="subItem.textContent" placeholder="请输入"
            @change="onCustomItemChange(subItem, 'INPUT')"></van-field>
          <span class="input-suffix-text" v-if="subItem.item.suffix">{{ subItem.item.suffix }}</span>
        </div>
      </div>
      <div v-if="subItem.item.itemType === 'TEXT'">
        <span class="text-hint-text">{{ subItem.item.textContent ? subItem.item.textContent : '--' }}</span>
      </div>
      <div v-if="subItem.item.itemType === 'DIVIDER'">
        <van-divider :style="{ color: '#DDDDDD', borderColor: '#DDDDDD', margin: '5px 0' }" />
      </div>
      <div v-if="subItem.item.itemType === 'ATTACHMENT'" class="attachment-container">
        <div class="attachment-title-container">
          <span class="attachment-title-text">{{ subItem.item.attachDesp ? subItem.item.attachDesp : '--' }}</span>
        </div>
        <div class="attachment-action-container">
          <div class="file-list-container">
            <div class="file-item-wrapper">
              <div class="file-item" @click="filePreview(subItem)">
                <div class="file-icon">
                  <van-icon name="description" color="#01a3f2" />
                </div>
                <div class="file-info">
                  <div class="file-name" :title="subItem.originalFileName">
                    {{ subItem.originalFileName }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="!disabled" class="upload-actions-container">
            <van-uploader capture="camera" accept=".pdf" :disabled="disabled"
              :after-read="(file, detail) => fileAction(file, detail, subItem)" max-count="1">
              <van-button class="upload-btn"><span v-if="subItem.fileId">已上传</span><span v-else>上传文件</span></van-button>
            </van-uploader>
          </div>
        </div>
      </div>
    </div>
    <!-- Recursively render child items if they exist -->

    <template v-if="item.retSubList && item.retSubList.length > 0">
      <tree-item v-for="subItem in item.retSubList" :key="subItem.id" :item="subItem" :disabled="disabled"
        @file-preview="filePreview" @custom-item-change="onCustomItemChange" @file-action="fileAction"
        class="sub-tree-item" />
    </template>
  </div>
</template>

<script>
export default {
  name: 'TreeItem',
  props: {
    item: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    filePreview(item) {
      this.$emit('file-preview', item)
    },
    onCustomItemChange(item, type) {
      this.$emit('custom-item-change', item, type)
    },
    fileAction(file, detail, item) {
      this.$emit('file-action', file, detail, item)
    }
  }
}
</script>

<style scoped lang="scss">
.tree-item {
  margin-left: 0;

  .rules-content {
    background: #effaff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #56bfee;
    font-size: 16px;
    color: #000000;
    padding: 8px 12px;
    line-height: 20px;
    margin-bottom: 10px;
  }

  .upload-btn {
    width: 99px;
    height: 40px;
    background: #ffffff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #01a3f2;
    font-weight: 500;
    font-size: 16px;
    color: #01a3f2;
    line-height: 19px;
  }

  .uni-input {
    border-radius: 10px 10px 10px 10px;
  }

  .item-container {
    margin-bottom: 10px;
    margin-left: 10px;
    font-size: 14px;
  }

  .text-content {
    display: inline-block;
    margin-right: 10px;
    max-width: 30vw;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .sub-tree-item {
    margin-left: 8px;
    padding-left: 10px;
    border-left: 2px solid #e0f1ff;
    position: relative;
  }

  .sub-tree-item::before {
    content: '';
    position: absolute;
    left: -2px;
    top: 0;
    width: 2px;
    height: 20px;
    background: linear-gradient(to bottom, #56bfee, #e0f1ff);
    border-radius: 1px;
  }

  .radio-title-container {
    margin-bottom: 8px;
  }

  .radio-title-text {
    display: block;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
    background: #f8fbff;
    border: 1px solid #e0f1ff;
    border-radius: 4px;
    padding: 8px 12px;
  }

  .radio-options-container {
    margin-left: 5px;
  }

  .input-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .input-prefix-container {
    margin-bottom: 4px;
  }

  .input-prefix-text {
    display: block;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
    background: #f8fbff;
    border: 1px solid #e0f1ff;
    border-radius: 4px;
    padding: 8px 12px;
  }

  .input-field-container {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .input-suffix-text {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
    margin-left: 8px;
  }

  .text-hint-container {
    margin-bottom: 8px;
  }

  .text-hint-text {
    display: block;
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
    background: #fafbfc;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px 12px;
  }

  .attachment-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .attachment-title-container {
    margin-bottom: 4px;
  }

  .attachment-title-text {
    display: block;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
    background: #f8fbff;
    border: 1px solid #e0f1ff;
    border-radius: 4px;
    padding: 8px 12px;
  }

  .attachment-action-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-left: 5px;
  }

  .file-list-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 100%;
  }

  .file-item-wrapper {
    width: 100%;
  }

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8fbff;
    border: 1px solid #e0f1ff;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;

    &:hover {
      background: #ebf8ff;
      border-color: #01a3f2;
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .file-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
  }

  .file-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .file-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
  }

  .file-index {
    font-size: 12px;
    color: #666;
    line-height: 1.2;
  }

  .file-actions {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(255, 59, 48, 0.1);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .upload-actions-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }

  .uploader-wrapper {
    flex-shrink: 0;
  }

  .upload-btn {
    min-width: 100px;
    height: 36px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #01a3f2;
    font-weight: 500;
    font-size: 14px;
    color: #01a3f2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #ebf8ff;
    }

    &:disabled {
      background: #f5f5f5;
      color: #d7d7d7;
      border-color: #e0e0e0;
      cursor: not-allowed;
    }
  }

  .save-btn {
    min-width: 100px;
    height: 36px;
    background: #01a3f2;
    border-radius: 6px;
    border: 1px solid #01a3f2;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: #0192d9;
    }

    &:disabled {
      background: #f5f5f5;
      color: #d7d7d7;
      border-color: #e0e0e0;
      cursor: not-allowed;
    }
  }

}
</style>
