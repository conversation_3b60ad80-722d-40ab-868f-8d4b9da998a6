<template>
  <div class="logo-content">
    <div @click="handleClick()">
      <img src="@/assets/img/<EMAIL>" class="img-return" alt="logo" />
    </div>
    <div @click="handleClick()" class="header-return">
      <span>返回</span>
    </div>
    <div class="header-title">
      {{ title }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'NavReturn',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClick() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.logo-content {
  display: flex;
  align-items: center;
  width: 320px;
  height: 60px;
  background: #ffffff;
  padding: 0 10px;

  .img-return {
    width: 20px;
    height: 20px;
  }

  .header-return {
    width: 40px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    margin-right: 20px;
  }

  .header-title {
    width: 220px;
    font-weight: bold;
    font-size: 16px;
    color: #124e7e;
    line-height: 23px;
    word-break: break-all; /* 添加此属性 */
  }
}
</style>
