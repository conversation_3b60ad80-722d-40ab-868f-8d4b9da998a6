<template>
  <div class="user-content">
    <div @click="exit()">
      <van-icon name="manager-o" />
    </div>
    <div @click="exit()">{{ expName }}</div>
    <div class="triangle-down" @click="exit()"></div>
    <van-dialog
      v-model="dialogShow"
      class="dialog-self"
      show-cancel-button
      :before-close="onBeforeClose"
      @confirm="dialogConfirm()"
      @cancel="dialogCancel()"
    >
      <van-form>
        <van-field
          class="uni-input"
          left-icon="lock"
          v-model="dialogValue"
          type="password"
          name="密码"
          placeholder="请输入修改后的密码"
          :rules="[{ required: true }]"
        />
      </van-form>
    </van-dialog>
    <van-popup v-model="show" position="right" class="exitPop" :style="{ width: '30%' }">
      <div class="exit-top">
        <div class="user-right">
          <span>
            <van-icon name="manager-o" />
            当前用户
          </span>
          <span>{{ this.expName }}</span>
        </div>
      </div>
      <van-button class="exit-btn" plain icon="edit" @click="handleResetPwd()">修改密码</van-button>
      <van-button class="exit-btn" plain icon="share-o" @click="logout()">退出登录</van-button>
    </van-popup>
  </div>
</template>

<script>
import { resetRouter } from '@/router'
import { resetUserPwd } from '@/api/user'
import { Toast } from 'vant'
import { Dialog } from 'vant'

export default {
  name: 'UserInfo',
  data() {
    return {
      expName: this.$store.state.user.expName,
      expId: this.$store.state.user.expId,
      userId: this.$store.state.user.expId,
      deptId: this.$store.state.user.deptId,
      dialogShow: false,
      dialogValue: '',
      dialogPlace: '',
      show: false
    }
  },
  methods: {
    /** 重置密码按钮操作 */
    handleResetPwd() {
      this.dialogValue = ''
      this.dialogPlace = ''
      this.dialogShow = true
    },
    /** 细则部分达标操作----弹框关闭前 */
    onBeforeClose(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done()
      }
    },
    /** 细则部分达标操作----弹框确认 */
    dialogConfirm() {
      console.log(this.userId)
      /* 定义验证表达式*/
      const regu = /^\S{6,}$/
      if (!regu.test(this.dialogValue)) {
        Toast({
          message: '密码不可少于6位，请重新输入',
          position: 'top'
        })
      } else {
        console.log(this.dialogValue)
        resetUserPwd(this.userId, this.dialogValue).then(response => {
          Toast({
            message: '修改成功，新密码是：' + this.dialogValue,
            position: 'top'
          })
          this.dialogShow = false
        })
      }
    },
    /** 细则部分达标操作----弹框取消 */
    dialogCancel() {
      this.dialogShow = false
    },
    exit() {
      this.show = true
    },
    goback() {
      this.$router.go(-1)
    },
    async logout() {
      Dialog.confirm({
        message: '您确定要退出登录吗？'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          resetRouter()
          location.reload()
          this.$router.push(`/login`)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user-content {
  width: 320px;
  padding-right: 20px;
  padding-left: 10px;
  display: flex;
  gap: 5px;
  align-items: center;
  justify-content: flex-end;
  font-weight: 400;
  font-size: 14px;
  color: #333333;

  .triangle-down {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 8px solid black;
    cursor: pointer;
  }
}

.uni-input {
  width: 300px;
  font-size: 15px;
  height: 40px;
  margin: 10px 0;
}

.dialog-self {
  padding: 10px;
}

.exitPop {
  width: 40%;
  height: 100%;
  background: #f5f5f5;

  .exit-top {
    display: flex;
    align-items: center;
    height: 100px;
    background: #124e7e;
    box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.5);
    color: #f5f5f5;
    padding-left: 10px;

    .user-right {
      margin-left: 5px;
      display: flex;
      flex-direction: column;
      font-size: 14px;

      span:nth-child(2) {
        font-size: 18px;
        line-height: 30px;
      }
    }
  }

  .exit-btn {
    margin-top: 10px;
    width: 100%;
  }
}
</style>
