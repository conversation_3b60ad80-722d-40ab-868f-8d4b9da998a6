import request from '@/utils/request'

// // 获取未提交的题
// export function getItems(query) {
//   return request({
//     url: '/xcjc/hosExpAssess/items',
//     method: 'get',
//     params: query
//   })
// }

// 获取未提交的题
export function getItems(data) {
  return request({
    url: '/xcjc/hosExpAssess/items',
    method: 'post',
    data: data
  })
}

// 获取已提交的题
export function selectCommitItem(query) {
  return request({
    url: '/xcjc/hosExpAssess/commitItem',
    method: 'get',
    params: query
  })
}

// 修改代号对应专家
export function updateItems(data) {
  return request({
    url: '/xcjc/hosExpAssess',
    method: 'put',
    data: data
  })
}

// 判断是否所有题都是已完成状态
export function isFinish(query) {
  return request({
    url: '/xcjc/hosExpAssess/isFinish',
    method: 'get',
    params: query
  })
}

// 专家进行提交操作
export function commit(query) {
  return request({
    url: '/xcjc/hosExpAssess/commit',
    method: 'get',
    params: query
  })
}
