<template>
  <van-popup round v-model="dialogShow" position="bottom" class="resultPop" :style="{ width: '100%' }"
    @close="updateDialogShow(false)">
    <div class="result-top">
      <div class="result-title">{{ score.name }}</div>
      <img @click="onCancel" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
    </div>
    <!-- 定量得分显示 -->
    <div v-if="score.type === '定量'" class="result-content">
      <div class="result-item" style="width: 100%">
        <div class="result-item-left">
          <div class="result-item-title">定量指标总得分</div>
          <div class="result-item-score">
            <span>{{ score.quantScore || '--' }}</span>
          </div>
        </div>
        <div v-if="score.showSelf" class="result-item-right">
          <div class="result-item-title">院内自评定量总得分</div>
          <div class="result-item-score">
            <span>{{ score.quantScoreSelf || '--' }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 现场且非选项的打分 -->
    <div v-if="score.type === '现场' && score.method !== '选项'" class="result-content">
      <div v-if="score.quantScore" class="result-item" style="width: 100%">
        <div class="result-item-left">
          <div class="result-item-title">定量指标总得分</div>
          <div class="result-item-score">
            <span>{{ score.quantScore || '--' }}</span>
          </div>
        </div>
        <div v-if="score.showSelf" class="result-item-right">
          <div class="result-item-title">院内自评定量总得分</div>
          <div class="result-item-score">
            <span>{{ score.quantScoreSelf || '--' }}</span>
          </div>
        </div>
      </div>
      <div class="result-item" style="width: 100%">
        <div class="result-item-left">
          <div class="result-item-title">现场指标总得分</div>
          <div class="result-item-score">
            <span>{{ score.siteScore || '--' }}</span>
          </div>
        </div>
        <div v-if="score.showSelf" class="result-item-right">
          <div class="result-item-title">院内自评现场总得分</div>
          <div class="result-item-score">
            <span>{{ score.siteScoreSelf || '--' }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 现场且选项的打分 -->
    <div v-if="score.type === '现场' && score.method === '选项'" class="result-content">
      <div v-if="score.quantScore" class="result-item" style="width: 100%">
        <div class="result-item-left">
          <div class="result-item-title">定量指标总得分</div>
          <div class="result-item-score">
            <span>{{ score.quantScore || '--' }}</span>
          </div>
        </div>
        <div v-if="score.showSelf" class="result-item-right">
          <div class="result-item-title">院内自评定量总得分</div>
          <div class="result-item-score">
            <span>{{ score.quantScoreSelf || '--' }}</span>
          </div>
        </div>
      </div>
      <div v-if="score.optionScore" class="result-item" style="width: 100%">
        <div class="result-item-left">
          <div class="result-item-title">现场检查标准指标</div>
          <div class="result-item-score">
            <span v-for="(item, index) in score.optionScore" :key="index">{{ item.name }}级：<span>{{ item.percent }}
                %&nbsp;&nbsp;&nbsp;</span></span>
            <span v-if="!score.optionScore || score.optionScore.length === 0">--</span>
          </div>
        </div>
        <div v-if="score.showSelf" class="result-item-right">
          <div class="result-item-title">院内自评现场检查标准指标</div>
          <div class="result-item-score">
            <span v-for="(item, index) in score.optionScoreSelf" :key="index">{{ item.name }}级：<span>{{
              item.percent }}
                %&nbsp;&nbsp;&nbsp;</span></span>
            <span v-if="!score.optionScoreSelf || score.optionScoreSelf.length === 0">--</span>
          </div>
        </div>
      </div>
      <div v-if="score.optionScoreCore" class="result-item" style="width: 100%">
        <div class="result-item-left">
          <div class="result-item-title">现场检查核心指标</div>
          <div class="result-item-score">
            <span v-for="(item, index) in score.optionScoreCore" :key="index">{{ item.name }}级：<span>{{ item.percent }}
                %&nbsp;&nbsp;&nbsp;</span></span>
            <span v-if="!score.optionScoreCore || score.optionScoreCore.length === 0">--</span>
          </div>
        </div>
        <div v-if="score.showSelf" class="result-item-right">
          <div class="result-item-title">院内自评现场检查核心指标</div>
          <div class="result-item-score">
            <span v-for="(item, index) in score.optionScoreCoreSelf" :key="index">{{ item.name }}级：<span>{{
              item.percent }}
                %&nbsp;&nbsp;&nbsp;</span></span>
            <span v-if="!score.optionScoreCoreSelf || score.optionScoreCoreSelf.length === 0">--</span>
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'ResultPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    score: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    onConfirm() {
      this.$emit('confirm')
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.resultPop {
  background-color: #fff;

  .result-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0;

    .result-title {
      height: 16px;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }

    .cancel-img {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }

  .result-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .result-item {
      height: 80px;
      background: linear-gradient(96deg, #e4edff 0%, #f2f6ff 100%);
      border-radius: 5px 5px 5px 5px;
      display: flex;
      flex-direction: row;
      padding: 0;

      .result-item-left,
      .result-item-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        padding: 0 20px;
      }

      .result-item-right {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 40px;
          background: linear-gradient(180deg, transparent 0%, #d0d9e8 20%, #a8b8d1 50%, #d0d9e8 80%, transparent 100%);
        }
      }

      .result-item-title {
        height: 18px;
        font-weight: 400;
        font-size: 16px;
        color: #000000;
      }

      .result-item-score {
        height: 26px;
        font-size: 14px;
        font-weight: bold;
        color: #124e7e;
        line-height: 23px;
      }
    }
  }
}
</style>
