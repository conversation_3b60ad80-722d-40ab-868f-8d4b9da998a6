import store from '@/store'

// /**
//  * @param {Array} value
//  * @returns {Boolean}
//  * @example see @/views/permission/directive.vue
//  */
// export default function checkPermission(value) {
//   if (value && value instanceof Array && value.length > 0) {
//     const roles = store.getters && store.getters.roles
//     const permissionRoles = value

//     const hasPermission = roles.some(role => {
//       return permissionRoles.includes(role)
//     })

//     if (!hasPermission) {
//       return false
//     }
//     return true
//   } else {
//     console.error(`need roles! Like v-permission="['admin','editor']"`)
//     return false
//   }
// }

// 权限检查方法
export function checkPermission(value) {
  // userId=1直接拥有所以权限
  const userId = store.getters.userId
  if (userId === 1) return true
  // 获取用户按钮权限
  let isExist = false
  const dynamicButtons = store.getters.buttons
  if (dynamicButtons === undefined || dynamicButtons === null || dynamicButtons.length < 1) {
    return isExist
  }
  dynamicButtons.forEach(button => {
    if (button === value) {
      isExist = true
      return isExist
    }
  })
  return isExist
}
