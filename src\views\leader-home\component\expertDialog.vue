<template>
  <van-dialog :value="dialogShow" :show-confirm-button="false" :show-cancel-button="false" class="dialog-self"
    title="专家信息" @confirm="onConfirm" @cancel="onCancel" @update:value="updateDialogShow">
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span style="font-weight: bold; color: #333333">专家信息</span>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
      </div>
    </template>
    <div class="content-container">
      <div v-for="(item, index) in expertData" :key="index" class="item-container">
        <div class="item-title">{{ item.tempGroupName }}</div>
        <div class="item-content">
          <div v-for="(subItem, subIndex) in item.expertList" :key="subIndex" class="subitem-container">
            <span class="subitem-name">
              {{ subItem.expertName }}
            </span>
            <span class="subitem-count">{{ item.tempGroupName === '定量指标' ? '定量条款监测数量：' : '现场条款监测数量：' }}
              {{ subItem.count != null ?
                subItem.count : subItem.totalCnt }}</span>
          </div>
        </div>
      </div>
      <div v-if="statusText !== '已完成' && statusText !== '复核中'"> <van-button @click="navToGrouped" type="default"
          class="grouped-btn">指标调整</van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'ExpertDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    expertData: {
      type: Array,
      default: null
    },
    statusText: {
      type: String,
      default: '未知状态'
    }
  },
  emits: ['confirm', 'cancel', 'nav', 'update:show'],
  data() {
    return {
      dialogShow: this.show
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    onConfirm() {
      this.$emit('confirm')
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    },
    navToGrouped() {
      this.$emit('nav')
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}

::v-deep .van-dialog__content {
  padding: 0 22px 22px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.dialog-self {
  width: 800px;
  max-height: 80vh;
}

.cancel-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.item-container {
  background: #ebf6ff;
  border-radius: 5px 5px 5px 5px;
  padding: 8px 10px;
  display: flex;
  align-items: center;
  gap: 5px;

  .item-title {
    width: 120px;
    height: 16px;
    font-weight: bold;
    font-size: 16px;
    color: #124e7e;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }
}

.subitem-container {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  min-width: 90px;
  height: 60px;
  background: #ffffff;
  border-radius: 5px 5px 5px 5px;
  padding: 5px;

  .subitem-name {
    padding: 0px 5px;
    height: 16px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    text-align: left;
  }

  .subitem-count {
    height: 16px;
    font-weight: 500;
    font-size: 14px;
    color: #999999;
    margin-left: 5px;
  }
}

.grouped-btn {
  width: 160px;
  height: 40px;
  background: #ffffff;
  border-radius: 30px 30px 30px 30px;
  border: 1px solid #01a3f2;
  font-weight: 500;
  font-size: 16px;
  color: #01a3f2;
}
</style>
