<template>
  <div class="page_container">
    <!-- 顶栏 20% 高度 -->
    <div class="top-bar">
      <HeadLogo />
      <div class="nav-content">
        <div style="display: flex; align-items: center;justify-content: center;">
          <div style="flex: 1;">
            <van-search v-model="searchValue" :clearable="false" shape="round" placeholder="请输入医院名称"
              @search="handleSearch" @cancel="handleReset" />
          </div>
          <div style="display: flex;">
            <van-button style="border: none;color: #01a3f2;" plain type="default" @click="handleSearch">搜索
            </van-button>
            <van-button style="border: none;color: #01a3f2;" plain type="default" @click="handleReset">重置</van-button>
          </div>
        </div>
      </div>
      <UserInfo />
    </div>

    <!-- 主体部分 -->
    <div class="main-container">
      <!-- 左侧侧边栏 20% 宽度 -->
      <div class="sidebar">
        <div v-for="(item, index) in years" :key="index" class="menu-item" :class="{ active: selectedYear === item }"
          @click="fetchDetails(item)">
          {{ item }}年度
        </div>
      </div>

      <!-- 右侧主要内容区域 -->
      <div class="main-content">
        <div class="menu-item">
          <van-tabs @click="handleSearch" v-model="activeName" type="card" swipeable color="#124e7e">
            <van-tab :title-style="{ border: 'none', borderRadius: '10px' }" v-for="(item, index) in navList"
              :key="index" :title="item.name" :name="item.id" />
          </van-tabs>
        </div>
        <van-empty v-if="details.length === 0" image="search" image-size="300px" description="无数据" />
        <div v-for="(item, index) in details" :key="index" class="menu-item"
          style="display: flex; flex-direction: column; justify-content: center">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; align-items: center; flex: 1; min-width: 0;">
              <div style="flex-shrink: 0;">
                <img src="@/assets/img/leader-home/<EMAIL>" class="img-hospital" alt="hospital" />
              </div>
              <div style="flex: 1; min-width: 0;">
                <div style="display: flex; align-items: center; flex-wrap: wrap;">
                  <span class="review-hospital-name">
                    {{ item.checkHosName }}
                  </span>
                  <span class="review-hospital-level">
                    {{
                      item.hospitalLevelCode === '20' ? '二级医院' : item.hospitalLevelCode === '30' ? '三级医院' : '--'
                    }}
                  </span>
                  <span class="review-hospital-level">
                    {{ item.planType === 'QUANTITATIVE_METRICS' ? '定量指标' : '现场评审' }}
                  </span>
                </div>
                <div class="review-group">评审组：{{ item.groupName }} 组长：{{ item.leaderName }}</div>
              </div>
            </div>
            <div @click="imageAction(item)"
              style="display: flex; align-items: center; cursor: pointer; flex-shrink: 0;">
              <div :class="statusClass(item)">
                {{ statusText(item) }}
              </div>
              <div class="nav-info-text">信息查询</div>
              <div>
                <img src="@/assets/img/member-home/<EMAIL>" class="img-link" alt="detail" />
              </div>
            </div>
          </div>
          <!-- <van-divider v-if="item.leader" /> -->
          <div v-if="item.leader" style="display: flex; justify-content: flex-end; align-items: center">
            <!-- <span class="review-score">院内自评总分：1800分</span> -->
            <van-button type="default" class="review-info-btn" @click="fetchHospitalDetail(item)">医院信息查询</van-button>
            <van-button type="default" class="review-look-btn" @click="fetchExpertGroupList(item)">查看分组</van-button>
            <van-button v-if="showResultButton(item)" type="default" class="review-look-btn"
              @click="openResult(item)">评审结果
            </van-button>
            <van-button v-if="showReviewButton(item)" type="default" class="review-start-btn"
              @click="navToReview(item)">组长复核
            </van-button>
            <van-button v-if="showStartButton(item)" type="default" class="review-start-btn"
              @click="openStart(item)">开启评审
            </van-button>
            <van-button v-if="showReReviewButton(item)" type="default" class="review-look-btn"
              @click="openRestart(item)">重新评审
            </van-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <expert-dialog :show.sync="groupDialog" :expert-data="expertData" :statusText="itemStatusText" @cancel="closeDialog"
      @nav="navToGrouped" />
    <info-dialog :show.sync="infoDialog" :info-data="infoData" @cancel="closeDialog" />
    <start-dialog :show.sync="startDialog" @cancel="closeDialog" @confirm="confirmStart" />
    <restart-dialog :show.sync="restartDialog" @cancel="closeDialog" @confirm="confirmRestart" />
    <result-popup :score="finalScore" :show.sync="resultDialog" @cancel="closeDialog" />
    <auth-dialog :show.sync="authDialog" />
  </div>
</template>

<script>
import {
  getExpertReviewYears,
  getReviewListByYear,
  getSiteGroupList,
  getQuantGroupList,
  startSiteReview,
  restartSiteReview,
  startQuantReview,
  restartQuantReview
} from 'api/leader-home'
import { getSiteResult, getQuantCal } from '@/api/leader-home/reviewPage'
import expertDialog from './component/expertDialog'
import infoDialog from './component/infoDialog'
import startDialog from './component/startDialog'
import restartDialog from './component/restartDialog'
import resultPopup from './component/resultPopup.vue'
import HeadLogo from '@/components/headLogo'
import UserInfo from '@/components/userInfo'
import store from '@/store'
import AuthDialog from './component/authDialog.vue'

export default {
  name: 'HosList',
  components: {
    HeadLogo,
    UserInfo,
    expertDialog,
    infoDialog,
    startDialog,
    restartDialog,
    resultPopup,
    AuthDialog
  },
  data() {
    return {
      activeName: '2',
      navList: [
        {
          name: '待评审',
          id: '1'
        },
        {
          name: '评审中',
          id: '2'
        },
        {
          name: '已完成',
          id: '3'
        }
      ],
      searchValue: '',
      userInfo: this.$store.state.user.userInfo,
      isHosExpert: this.$store.state.user.userInfo.roles.includes('yydjpsxt_hos_expert'),
      years: [],
      details: [],
      copyDetails: [],
      selectedYear: null, // 新增：存储当前选中的年份
      groupDialog: false,
      infoDialog: false,
      startDialog: false,
      restartDialog: false,
      resultDialog: false,
      authDialog: false,
      selectedItem: null,
      itemStatusText: null,
      expertData: [],
      infoData: {},
      finalScore: {}
    }
  },
  created() {
    this.fetchYears()
  },
  mounted() {
    // 死数据 不同发版可能需要更改
    if (store.getters.isNeedAuth) {
      if (store.getters.isAuth) {
        console.log('已完成认证')
      } else {
        console.log('未完成认证')
        // this.authDialog = true
      }
    } else {
      console.log('不需要认证')
    }
  },
  computed: {},
  methods: {
    handleSearch() {
      console.log(this.activeName, this.searchValue)
      // 先按照名称搜索
      this.details = this.copyDetails.filter(item => item.checkHosName.includes(this.searchValue))
      // 按照状态搜索
      const navName = this.navList.find(item => item.id === this.activeName).name
      if (navName === '待评审') {
        this.details = this.details.filter(item => this.statusText(item) === '未开始' || this.statusText(item) === '未知状态')
      } else if (navName === '评审中') {
        this.details = this.details.filter(item => this.statusText(item) === '评审中' || this.statusText(item) === '复核中')
      } else if (navName === '已完成') {
        this.details = this.details.filter(item => this.statusText(item) === '已完成')
      }
    },
    handleReset() {
      this.searchValue = ''
      this.handleSearch()
    },
    fetchYears() {
      getExpertReviewYears({}).then(res => {
        console.log(res)
        this.years = res.rows.map(item => item.reviewYear)
        if (this.years.length > 0) {
          this.selectedYear = this.years[0] // 默认选中第一个年份
          this.fetchDetails(this.selectedYear)
        } else {
          this.years.push(String(new Date().getFullYear()))
          this.selectedYear = this.years[0] // 默认选中第一个年份
          this.fetchDetails(this.selectedYear)
        }
      })
    },
    fetchDetails(item) {
      this.selectedYear = item // 点击年份时，更新 selectedYear
      getReviewListByYear({ reviewYear: item }).then(res => {
        console.log(res)
        this.details = res.rows
        this.copyDetails = res.rows
        this.searchValue = ''
        this.activeName = '2'
        this.handleSearch()
      })
    },
    fetchHospitalDetail(item) {
      this.infoDialog = true
      this.infoData = {
        name: item.checkHosName,
        level: item.hospitalLevelCode === '20' ? '二级医院' : item.hospitalLevelCode === '30' ? '三级医院' : '--',
        type: item.planType === 'ON_SITE_REVIEW' ? '现场评审' : '定量指标',
        group: item.groupName,
        leader: item.leaderName
      }
      console.log(this.infoData)
    },
    fetchExpertGroupList(item) {
      this.selectedItem = item
      this.itemStatusText = this.statusText(item)
      if (item.planType === 'QUANTITATIVE_METRICS') {
        getQuantGroupList({ taskId: item.taskId }).then(res => {
          console.log(res)
          this.expertData = [
            {
              tempGroupName: '定量指标',
              expertList: res.data
            }
          ]
          console.log(this.expertData)
          this.groupDialog = true
        })
      } else {
        getSiteGroupList({ recordId: item.recordId }).then(res => {
          console.log(res)
          this.expertData = res.data
          console.log(this.expertData)
          this.groupDialog = true
        })
      }
    },
    fetchFinalScore(item) {
      if (item.planType === 'ON_SITE_REVIEW') {
        getSiteResult({ recordId: item.recordId }).then(res => {
          console.log(res)
          this.finalScore = {
            name: item.checkHosName,
            type: this.typeText(item),
            method: this.methodText(item),
            showSelf: !this.isHosExpert,
            quantScore: res.data[0].famxId ? res.data[0].quatScore : null,
            siteScore: this.methodText(item) === '选项' ? null : res.data[0].totalScore,
            optionScore: this.methodText(item) === '选项' ? res.data[0].optionsScore : null,
            optionScoreCore: this.methodText(item) === '选项' ? res.data[0].optionsCore : null,
            quantScoreSelf: res.data[0].selfRet && res.data[0].famxId ? res.data[0].selfRet.quatScore : null,
            siteScoreSelf: res.data[0].selfRet && this.methodText(item) !== '选项' ? res.data[0].selfRet.totalScore : null,
            optionScoreSelf: res.data[0].selfRet && this.methodText(item) === '选项' ? res.data[0].selfRet.optionsScore : null,
            optionScoreCoreSelf: res.data[0].selfRet && this.methodText(item) === '选项' ? res.data[0].selfRet.optionsCore : null
          }
          this.resultDialog = true
          console.log(this.finalScore)
        })
      } else {
        getQuantCal(item.taskId).then(res => {
          console.log(res)
          this.finalScore = {
            name: item.checkHosName,
            type: this.typeText(item),
            method: this.methodText(item),
            showSelf: !this.isHosExpert,
            quantScore: res.data.totalScore,
            quantScoreSelf: null
          }
          this.resultDialog = true
          console.log(this.finalScore)
        })
      }
    },
    openStart(item) {
      this.startDialog = true
      this.selectedItem = item
    },
    confirmStart() {
      console.log('confirm start')
      if (this.selectedItem.planType === 'QUANTITATIVE_METRICS') {
        startQuantReview(this.selectedItem.taskId).then(res => {
          this.fetchDetails(this.selectedYear)
          this.closeDialog()
        })
      } else {
        startSiteReview({ recordId: this.selectedItem.recordId }).then(res => {
          this.fetchDetails(this.selectedYear)
          this.closeDialog()
        })
      }
    },
    openRestart(item) {
      this.restartDialog = true
      this.selectedItem = item
    },
    confirmRestart() {
      console.log('confirm restart')
      if (this.selectedItem.planType === 'QUANTITATIVE_METRICS') {
        restartQuantReview(this.selectedItem.taskId).then(res => {
          this.fetchDetails(this.selectedYear)
          this.closeDialog()
        })
      } else {
        restartSiteReview({ recordId: this.selectedItem.recordId }).then(res => {
          this.fetchDetails(this.selectedYear)
          this.closeDialog()
        })
      }
    },
    openResult(item) {
      this.selectedItem = item
      this.fetchFinalScore(item)
      console.log('open result')
    },
    closeDialog() {
      this.resultDialog = false
      this.startDialog = false
      this.groupDialog = false
      this.restartDialog = false
      this.infoDialog = false
    },
    navToReview(item) {
      const statusText = this.statusText(item)
      this.$router.push({
        path: '/leader-review',
        query: {
          status: statusText,
          method: this.methodText(item),
          reviewMode: item.reviewMode,
          type: this.typeText(item),
          taskId: item.taskId,
          recordId: item.recordId,
          reviewYear: item.reviewYear,
          hosName: item.checkHosName,
          configPlan: item.configPlan,
          reviewType: item.reviewType,
          standardId: item.standardId,
          templateId: item.templateId
        }
      })
    },
    imageAction(item) {
      if (item.leader) {
        this.navToReview(item)
      } else {
        const statusText = this.statusText(item)
        let path = ''
        if (statusText === '未开始') {
          path = '/member-not-started'
        } else if (statusText === '评审中') {
          path = '/member-in-review'
        } else if (statusText === '复核中') {
          path = '/member-in-re-review'
        } else if (statusText === '已完成') {
          path = '/member-completed'
        }
        this.$router.push({
          path: path,
          query: {
            status: statusText,
            method: this.methodText(item),
            reviewMode: item.reviewMode,
            type: this.typeText(item),
            taskId: item.taskId,
            recordId: item.recordId,
            reviewYear: item.reviewYear,
            hosName: item.checkHosName,
            configPlan: item.configPlan,
            reviewType: item.reviewType,
            standardId: item.standardId,
            templateId: item.templateId
          }
        })
      }
    },
    navToGrouped(item = this.selectedItem) {
      const statusText = this.statusText(item)
      const path = '/leader-grouped'
      this.$router.push({
        path: path,
        query: {
          status: statusText,
          method: this.methodText(item),
          reviewMode: item.reviewMode,
          type: this.typeText(item),
          taskId: item.taskId,
          recordId: item.recordId,
          groupId: item.groupId,
          reviewYear: item.reviewYear,
          hosName: item.checkHosName,
          configPlan: item.configPlan,
          reviewType: item.reviewType,
          standardId: item.standardId,
          templateId: item.templateId
        }
      })
    },
    typeText(item) {
      let typeText = ''
      if (item.planType === 'QUANTITATIVE_METRICS') {
        typeText = '定量'
      } else if (item.planType === 'ON_SITE_REVIEW') {
        typeText = '现场'
      }
      return typeText
    },
    methodText(item) {
      let reviewModeText = ''
      if (item.reviewMode === 'SCORE') {
        reviewModeText = '打分'
      } else if (item.reviewMode === 'OPTION') {
        reviewModeText = '选项'
      } else {
        reviewModeText = '自定义'
      }
      return reviewModeText
    },
    // 根据状态返回不同的文本
    statusText(item) {
      if (item.planType === 'QUANTITATIVE_METRICS') {
        if (item.evalStatus === 'REVIEWED' || item.evalStatus === 'FINISHED') {
          return '已完成'
        } else if (item.evalStatus === 'SCORED' || item.evalStatus === 'WAIT_CHECK') {
          return '复核中'
        } else if (item.evalStatus === 'BEGIN' || item.evalStatus === 'DOING') {
          return '评审中'
        } else {
          return '未开始'
        }
      } else if (item.planType === 'ON_SITE_REVIEW') {
        if (item.reviewStatus === 'FINISHED') {
          return '已完成'
        } else if (item.reviewStatus === 'WAIT_CHECK') {
          return '复核中'
        } else if (item.reviewStatus === 'DOING') {
          return '评审中'
        } else {
          return '未开始'
        }
      }
      return '未知状态'
    },
    // 根据状态返回不同的样式类
    statusClass(item) {
      let status = ''
      if (item.planType === 'QUANTITATIVE_METRICS') {
        status = item.evalStatus
      } else if (item.planType === 'ON_SITE_REVIEW') {
        status = item.reviewStatus
      }

      switch (status) {
        case 'FINISHED':
        case 'REVIEWED':
          return 'review-status-completed'
        case 'WAIT_CHECK':
        case 'SCORED':
        case 'DOING':
        case 'BEGIN':
          return 'review-status-reviewing'
        default:
          return 'review-status-unstarted'
      }
    },
    showReviewButton(item) {
      // 评审复核
      if (item.planType === 'QUANTITATIVE_METRICS') {
        return (
          item.evalStatus === 'SCORED' ||
          item.evalStatus === 'WAIT_CHECK' ||
          item.evalStatus === 'BEGIN' ||
          item.evalStatus === 'DOING'
        )
      } else if (item.planType === 'ON_SITE_REVIEW') {
        return item.reviewStatus === 'WAIT_CHECK' || item.reviewStatus === 'DOING'
      }
      return false
    },
    showResultButton(item) {
      // 非医院专家，则显示结果按钮
      if (!this.isHosExpert) {
        return true
      }
      // 查看结果
      if (item.planType === 'QUANTITATIVE_METRICS') {
        return item.evalStatus === 'REVIEWED' || item.evalStatus === 'FINISHED'
      } else if (item.planType === 'ON_SITE_REVIEW') {
        return item.reviewStatus === 'FINISHED'
      }
      return false
    },
    showStartButton(item) {
      // 开启评审
      if (item.planType === 'QUANTITATIVE_METRICS') {
        return !(
          item.evalStatus === 'REVIEWED' ||
          item.evalStatus === 'FINISHED' ||
          item.evalStatus === 'SCORED' ||
          item.evalStatus === 'WAIT_CHECK' ||
          item.evalStatus === 'BEGIN' ||
          item.evalStatus === 'DOING'
        )
      } else if (item.planType === 'ON_SITE_REVIEW') {
        return !(
          item.reviewStatus === 'FINISHED' ||
          item.reviewStatus === 'WAIT_CHECK' ||
          item.reviewStatus === 'DOING'
        )
      }
      return false
    },
    showReReviewButton(item) {
      // 重新评审
      if (item.planType === 'QUANTITATIVE_METRICS') {
        return item.evalStatus === 'REVIEWED' || item.evalStatus === 'FINISHED'
      } else if (item.planType === 'ON_SITE_REVIEW') {
        return item.reviewStatus === 'FINISHED'
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .van-tabs__nav--card {
  border: none;
}

.page_container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f2f5f8;

  .top-bar {
    height: 60px;
    flex-shrink: 0;
    background: #ffffff;
    display: flex;
    align-items: center;

    .nav-content {
      flex: 1;
    }
  }

  .main-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .sidebar {
      background: #ffffff;
      display: flex;
      flex-direction: column;
      align-items: center;
      /* justify-content: center;  删除垂直居中, 让列表从顶部开始显示 */
      width: 160px;
      height: calc(100vh - 60px);
      overflow-y: auto;
      padding-top: 10px;

      /* 增加顶部内边距，使列表不紧贴顶部 */
      .menu-item {
        display: flex;
        align-items: center;
        justify-content: center;
        /* 添加水平居中 */
        width: 124px;
        min-height: 40px;
        border-radius: 20px 20px 20px 20px;
        font-weight: bold;
        font-size: 16px;
        color: #666666;
        margin: 0;
        cursor: pointer;

        &.active {
          background: #124e7e;
          color: #ffffff;
        }
      }
    }

    .main-content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;

      .img-hospital {
        width: 62px;
        height: 62px
      }

      .nav-info-text {
        font-size: 12px;
        margin-left: 10px;
        margin-top: 2px;
        color: #666666;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .img-link {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }

      .menu-item {
        box-sizing: border-box;
        width: 100%;
        // height: 176px;
        // /* 确保高度生效 */
        // min-height: 176px;
        /* 增加最小高度，防止内容为空时高度塌陷 */
        background: #ffffff;
        border-radius: 20px 20px 20px 20px;
        margin: 5px 0;
        padding: 10px;

        .review-hospital-name {
          max-width: 400px;
          height: 28px;
          font-weight: bold;
          font-size: 16px;
          color: #000000;
          display: flex;
          align-items: center;
          margin-right: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex-shrink: 0;
        }

        .review-hospital-level {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 0 5px;
          height: 24px;
          background: #124e7e;
          border-radius: 5px 5px 5px 5px;
          margin-right: 10px;

          font-weight: 500;
          font-size: 12px;
          color: #ffffff;
        }

        .review-group {
          max-width: 100%;
          height: 22px;
          font-weight: 500;
          font-size: 14px;
          color: #7d94a7;
          margin-top: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .review-status-unstarted {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 32px;
          background: #ececec;
          border-radius: 10px 10px 10px 10px;
          font-weight: 500;
          font-size: 16px;
          color: #666666;
          line-height: 19px;
        }

        .review-status-reviewing {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 32px;
          background: #e0f3ff;
          border-radius: 10px 10px 10px 10px;
          font-weight: 500;
          font-size: 16px;
          color: #0075e2;
        }

        .review-status-completed {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 32px;
          background: #e2ffe0;
          border-radius: 10px 10px 10px 10px;
          font-weight: 500;
          font-size: 16px;
          color: #15b100;
        }

        .review-score {
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16px;
          color: #01a3f2;
          background: #f0f9ff;
          border-radius: 30px;
          border: 1px solid #e0f3ff;
          padding: 0 12px;
          box-sizing: border-box;
          margin-right: 10px;
        }

        .review-info-btn {
          width: 160px;
          height: 40px;
          background: #ffffff;
          border-radius: 30px 30px 30px 30px;
          border: 1px solid #01a3f2;
          font-weight: 500;
          font-size: 16px;
          color: #01a3f2;
          margin-right: 10px;
        }

        .review-look-btn {
          width: 115px;
          height: 40px;
          background: #ffffff;
          border-radius: 30px 30px 30px 30px;
          border: 1px solid #01a3f2;
          font-weight: 500;
          font-size: 16px;
          color: #01a3f2;
          margin-right: 10px;
        }

        .review-start-btn {
          width: 115px;
          height: 40px;
          background: #01a3f2;
          border-radius: 30px 30px 30px 30px;
          border: 1px solid #01a3f2;
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
