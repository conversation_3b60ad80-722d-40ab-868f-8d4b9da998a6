<template>
  <van-dialog
    :value="dialogShow"
    :show-confirm-button="false"
    :show-cancel-button="false"
    class="dialog-self"
    title="身份认证"
    @update:value="updateDialogShow"
  >
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span style="font-weight: bold;  color: #333333">身份认证</span>
        <img @click="handleClose" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
      </div>
    </template>
    <div class="content-container">
      <van-form ref="form" :model="form">
        <div class="form-item">
          <van-field
            v-model="form.name"
            name="name"
            label="姓名"
            placeholder="请输入姓名"
            :rules="[{ required: true, message: '请填写姓名' }]"
          />
        </div>
        <div class="form-item">
          <van-field
            v-model="form.idCard"
            name="idCard"
            label="身份证号"
            placeholder="请填写身份证"
            clearable
            :rules="[{ required: true, validator: validateIdCard, message: '请输入合法的身份证号码' }]"
          />
        </div>
        <div class="form-item">
          <van-field
            v-model="form.phone"
            name="phone"
            label="手机号码"
            placeholder="请输入手机号码"
            maxlength="11"
            :rules=rules
          />
        </div>
      </van-form>
      <div class="button-container">
        <van-button type="default" class="cancel-button" @click="handleCancel">取消</van-button>
        <van-button type="primary" class="submit-button" @click="submitBatch">提交</van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script>
import { identityCodeValid } from '@/utils/idCardUtils'
import { identityAuth } from '@/api/leader-home/index'
import store from '@/store'
import { Toast } from 'vant'

export default {
  name: 'AuthDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show,
      form: {
        name: '',
        idCard: '',
        phone: ''
      },
      loading: false,
      redirect: undefined,
      loginCallback: null,
      cancelCallback: null,
      rules: [{ required: true, pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码' }]
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    validateIdCard(val) {
      if (!val) {
        return false
      }
      return identityCodeValid(val)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    },
    handleClose() {
      this.logoutAndClose()
    },
    handleCancel() {
      this.logoutAndClose()
    },
    logoutAndClose() {
      this.$dialog
        .confirm({
          title: '提示',
          message: '确认退出登录吗?'
        })
        .then(() => {
          store.dispatch('LogOut').then(() => {
            this.dialogShow = false
            this.$emit('update:show', false)
            location.reload()
          })
        })
        .catch(() => {
          Toast.fail('已取消退出')
        })
    },
    submitBatch() {
      this.$refs.form
        .validate()
        .then(() => {
          // 死数据 不同发版可能需要更改
          Toast.success('认证成功')
          store.commit('SET_ISAUTH', true)
          this.dialogShow = false
          this.$emit('update:show', false)
          // identityAuth(this.form).then(res => {
          //   Toast.success('认证成功')
          //   store.commit('SET_ISAUTH', true)
          //   this.dialogShow = false
          //   this.$emit('update:show', false)
          // })
        })
        .catch(error => {
          console.log('表单校验失败', error)
        })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}

::v-deep .van-dialog__content {
  padding: 0 22px 22px;
}

.dialog-self {
  width: 500px;
}

.cancel-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item {
  height: 70px;
  position: relative;
}

.button-container {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.submit-button {
  width: 75px;
  background: #124e7e;
  border-radius: 3px;
}

.cancel-button {
  width: 75px;
  background: #ffffff;
  border-radius: 3px;
  border: 1px solid #124e7e;
  color: #124e7e;
}
</style>
