<template>
  <van-popup
    round
    v-model="dialogShow"
    position="bottom"
    class="resultPop"
    :style="{ width: '100%' }"
    @close="updateDialogShow(false)"
  >
    <div>
      <div class="result-top">
        <div class="result-title">请签名后，点击“提交”按钮提交该医院评审结果</div>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" width="24px" height="24px" alt="close" />
      </div>
      <div class="sign-container">
        <div class="sign-title">签名区域</div>
        <canvas ref="signatureCanvas" class="sign-area" style="border: 1px solid #ccc; height: 350px"></canvas>
      </div>
      <div class="action-container">
        <div>
          <van-button type="default" class="clear-btn" @click="clearSignature">清除签名</van-button>
          <van-button type="default" class="cancel-btn" @click="onCancel">取消</van-button>
          <van-button type="default" class="confirm-btn" @click="onConfirm">确定</van-button>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { upload } from '@/api/member-home/common'
import { Toast } from 'vant'
export default {
  name: 'SubmitPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show,
      signaturePad: null,
      isSigned: false
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
      if (newValue) {
        this.$nextTick(() => {
          this.initSignaturePad()
        })
      }
    }
  },
  methods: {
    initSignaturePad() {
      const canvas = this.$refs.signatureCanvas
      if (!canvas) return // 添加空值检查

      const ctx = canvas.getContext('2d')

      // 高清屏适配
      const dpr = window.devicePixelRatio || 1
      const rect = canvas.getBoundingClientRect()
      canvas.width = rect.width * dpr
      canvas.height = rect.height * dpr
      ctx.scale(dpr, dpr)

      // Fill background with white
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      this.signaturePad = {
        ctx,
        drawing: false,
        lastX: 0,
        lastY: 0
      }

      // 鼠标事件监听
      canvas.addEventListener('mousedown', this.startDrawing)
      canvas.addEventListener('mousemove', this.draw)
      canvas.addEventListener('mouseup', this.stopDrawing)
      canvas.addEventListener('mouseout', this.stopDrawing)

      // 触摸事件监听
      canvas.addEventListener('touchstart', this.startDrawing)
      canvas.addEventListener('touchmove', this.draw)
      canvas.addEventListener('touchend', this.stopDrawing)
    },

    startDrawing(e) {
      if (!this.signaturePad) return // 添加空值检查
      this.signaturePad.drawing = true
      const rect = this.$refs.signatureCanvas.getBoundingClientRect()
      const x = (e.clientX || e.touches[0].clientX) - rect.left
      const y = (e.clientY || e.touches[0].clientY) - rect.top

      this.signaturePad.lastX = x
      this.signaturePad.lastY = y
      this.isSigned = true
    },

    draw(e) {
      if (!this.signaturePad?.drawing) return // 使用可选链操作符
      const ctx = this.signaturePad.ctx
      const rect = this.$refs.signatureCanvas.getBoundingClientRect()
      const x = (e.clientX || e.touches[0].clientX) - rect.left
      const y = (e.clientY || e.touches[0].clientY) - rect.top

      ctx.beginPath()
      ctx.moveTo(this.signaturePad.lastX, this.signaturePad.lastY)
      ctx.lineTo(x, y)
      ctx.strokeStyle = '#000'
      ctx.lineWidth = 2
      ctx.lineCap = 'round'
      ctx.stroke()

      this.signaturePad.lastX = x
      this.signaturePad.lastY = y
    },

    stopDrawing() {
      if (this.signaturePad) {
        this.signaturePad.drawing = false
      }
    },

    clearSignature() {
      if (!this.signaturePad) return
      const ctx = this.signaturePad.ctx
      const canvas = this.$refs.signatureCanvas
      ctx.fillStyle = '#fff' // Set fill color to white
      ctx.fillRect(0, 0, canvas.width, canvas.height) // Fill the canvas with white
      this.isSigned = false
    },

    async onConfirm() {
      if (!this.isSigned) {
        Toast.fail('请先签名')
        return
      }

      // 将canvas转换为图片
      const canvas = this.$refs.signatureCanvas
      const imageData = canvas.toDataURL('image/png')
      const blob = this.dataURLtoBlob(imageData)

      // 上传签名图片
      const formData = new FormData()
      formData.append('file', blob, 'signature.png')
      try {
        const res = await upload(formData)
        this.$emit('confirm', res.data.fileId)
        this.updateDialogShow(false)
      } catch (error) {
        console.error('签名上传失败', error)
      }
    },

    dataURLtoBlob(dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },

    onCancel() {
      this.clearSignature()
      this.$emit('cancel')
      this.updateDialogShow(false)
    },

    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>

<style lang="scss" scoped>
/* 样式保持不变 */
.resultPop {
  background-color: #fff;
  .result-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0px;
    margin-bottom: 10px;
    .result-title {
      height: 16px;
      font-weight: bold;
      font-size: 18px;
      color: #333333;
    }
  }
  .sign-container {
    margin: 0 20px 10px;
    background: #f5f5f5;
    border-radius: 10px 10px 10px 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    .sign-title {
      height: 16px;
      font-weight: bold;
      font-size: 18px;
      color: #999999;
      margin-bottom: 10px;
    }
    .sign-area {
      background: white;
      touch-action: none;
    }
  }
  .action-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    > div {
      display: flex;
      gap: 10px;
    }
    .clear-btn {
      width: 172px;
      height: 50px;
      background: #ff4444;
      border-radius: 5px;
      border: 1px solid #ff4444;
      font-weight: 500;
      font-size: 17px;
      color: #ffffff;
    }
    .cancel-btn {
      width: 172px;
      height: 50px;
      background: #ffffff;
      border-radius: 5px;
      border: 1px solid #d7d7d7;
      font-weight: 500;
      font-size: 17px;
      color: #333333;
    }
    .confirm-btn {
      width: 172px;
      height: 50px;
      background: #01a3f2;
      border-radius: 5px;
      font-weight: 500;
      font-size: 17px;
      color: #ffffff;
    }
  }
}
</style>
