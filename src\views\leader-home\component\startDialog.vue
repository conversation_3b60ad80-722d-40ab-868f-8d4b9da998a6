<template>
  <van-dialog :value="dialogShow" :show-confirm-button="false" :show-cancel-button="false" class="dialog-self"
    title="专家信息" @confirm="onConfirm" @cancel="onCancel" @update:value="updateDialogShow">
    <template #title>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <span style="font-weight: bold; color: #333333">提示</span>
        <img @click="onCancel" src="@/assets/img/<EMAIL>" class="cancel-img" alt="close" />
      </div>
    </template>
    <div class="content-container">
      <div class="container-name">确定要开启评审吗？</div>
      <div>
        <van-button type="default" class="cancel-btn" @click="onCancel">取消</van-button>
        <van-button type="default" class="confirm-btn" @click="onConfirm">确定</van-button>
      </div>
    </div>
  </van-dialog>
</template>

<script>
export default {
  name: 'StartDialog',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'cancel', 'update:show'],
  data() {
    return {
      dialogShow: this.show
    }
  },
  watch: {
    show(newValue) {
      this.dialogShow = newValue
    }
  },
  methods: {
    onConfirm() {
      this.$emit('confirm')
      this.$emit('update:show', false)
    },
    onCancel() {
      this.$emit('cancel')
      this.$emit('update:show', false)
    },
    updateDialogShow(value) {
      this.dialogShow = value
      this.$emit('update:show', value)
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .van-dialog__header {
  padding: 15px 22px 10px;
}

::v-deep .van-dialog__content {
  padding: 0 22px 22px;
}

.dialog-self {
  width: 500px;
}

.cancel-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;

  .container-name {
    display: flex;
    align-items: center;
    min-height: 150px
  }

  .cancel-btn {
    width: 172px;
    height: 50px;
    background: #ffffff;
    border-radius: 5px 5px 5px 5px;
    border: 1px solid #d7d7d7;
    font-weight: 500;
    font-size: 17px;
    color: #333333;
    margin-right: 10px;
  }

  .confirm-btn {
    width: 172px;
    height: 50px;
    background: #01a3f2;
    border-radius: 5px 5px 5px 5px;
    font-weight: 500;
    font-size: 17px;
    color: #ffffff;
  }
}
</style>
