import request from '@/utils/request'

// 获取用户详细信息
export function getHos(expId) {
  return request({
    url: '/xcjc/hosExpAssignRecord/hos/' + expId,
    method: 'get'
  })
}

// 获取用户详细信息
export function getAssignRecord(id) {
  return request({
    url: '/xcjc/hosExpAssignRecord/' + id,
    method: 'get'
  })
}

// 获取用户详细信息
export function isLeader(query) {
  return request({
    url: '/xcjc/expertGroup/leader',
    method: 'get',
    params: query
  })
}
