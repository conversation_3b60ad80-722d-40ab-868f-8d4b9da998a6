import axios from 'axios'
import store from '@/store'
import {
  getToken,
  setRefreshToken,
  getExchangeToken,
  setToken,
  removeExchangeToken,
  getRefreshToken
} from '@/utils/auth'
import { Dialog } from 'vant'
import { Toast } from 'vant'
// 根据环境不同引入不同api地址
// import { baseApi } from '@/config'

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base api url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 80000// request timeout
})

// request拦截器 request interceptor
service.interceptors.request.use(
  config => {
    // 不传递默认开启loading
    if (!config.hideloading) {
      // loading
      Toast.loading({
        forbidClick: true
      })
    }
    const isToken = (config.headers || {}).isToken === false
    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    } else {
      // support form-data
      config.headers['Content-Type'] = 'application/x-www-form-urlencoded;charset=utf-8'
    }
    // config.headers['Authorization'] = 'Bearer f9599b50-e83f-40dd-a21d-b60c30132d6b'
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)
// respone拦截器
service.interceptors.response.use(
  response => {
    Toast.clear()
    const res = response.data
    const code = res.code || 200 || '0'
    if (code === 401) {
      Dialog.confirm({
        title: '系统提示',
        message: '登录状态已过期，您可以继续留在该页面，或者重新登录'
      })
        .then(() => {
          store.dispatch('LogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
          // on confirm
        })
        .catch(() => {})
    } else if (code === 500) {
      Toast.fail(res.msg)
      return Promise.reject(new Error(res.msg))
    } else if (code !== 200 && code !== '0') {
      return Promise.reject('error')
    } else {
      if (getExchangeToken()) {
        setToken(getExchangeToken())
        setRefreshToken(getRefreshToken()) // 刷新token
        store.commit('SET_TOKEN', getExchangeToken())
        removeExchangeToken()
      }
      window.isReresh = false
      return res
    }
    // if (res.status && res.status !== 200) {
    //   // 登录超时,重新登录
    //   if (res.status === 401) {
    //     store.dispatch('FedLogOut').then(() => {
    //       location.reload()
    //     })
    //   }
    //   return Promise.reject(res || 'error')
    // } else {
    //   return Promise.resolve(res)
    // }
  },
  error => {
    Toast.clear()
    console.log('err' + error) // for debug
    return Promise.reject(error)
  }
)

export default service
