import request from '@/utils/request'

// 通过组长ID查询组内成员进度信息
export function getExpProgress(query) {
  return request({
    url: '/xcjc/hosExpAssess/progress',
    method: 'get',
    params: query
  })
}

// 通过组长ID查询组内成员进度信息
export function reject(query) {
  return request({
    url: '/xcjc/hosExpAssess/reject',
    method: 'get',
    params: query
  })
}

// 通过组长ID查询组内成员进度信息
export function allFinish(query) {
  return request({
    url: '/xcjc/hosExpAssess/allFinish',
    method: 'get',
    params: query
  })
}

// 提交医院评审并计算分数
export function commitHos(id) {
  return request({
    url: '/xcjc/hosExpAssignRecord/commit/' + id,
    method: 'get'
  })
}

// 修改代号对应专家
export function updateExpAssignRecord(data) {
  return request({
    url: '/xcjc/hosExpAssignRecord',
    method: 'put',
    data: data
  })
}
